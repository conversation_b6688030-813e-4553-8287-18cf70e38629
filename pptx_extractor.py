#!/usr/bin/env python3
"""
Comprehensive PPTX Content Extractor

This script extracts all content and structural information from PPTX files
and converts it into a structured format for recreation purposes.

Author: AI Assistant
Date: 2025-07-13
"""

import os
import json
import base64
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
import zipfile
import xml.etree.ElementTree as ET

from pptx import Presentation
from pptx.enum.shapes import MSO_SHAPE_TYPE
from pptx.enum.text import MSO_ANCHOR, MSO_AUTO_SIZE
from pptx.util import Inches, Pt
from pptx.dml.color import RGBColor

class PPTXExtractor:
    """Comprehensive PPTX content and structure extractor."""
    
    def __init__(self, output_dir: str = "extracted_pptx_data"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
    def extract_all_pptx_files(self, directory: str = ".") -> Dict[str, Any]:
        """Extract all PPTX files in the specified directory."""
        directory_path = Path(directory)
        pptx_files = list(directory_path.glob("*.pptx"))
        
        if not pptx_files:
            print(f"No PPTX files found in {directory}")
            return {}
        
        extracted_data = {}
        
        for pptx_file in pptx_files:
            print(f"Processing: {pptx_file.name}")
            try:
                file_data = self.extract_pptx_content(str(pptx_file))
                extracted_data[pptx_file.name] = file_data
                
                # Save individual file data
                output_file = self.output_dir / f"{pptx_file.stem}_extracted.json"
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(file_data, f, indent=2, ensure_ascii=False, default=str)
                print(f"Saved extraction to: {output_file}")
                
            except Exception as e:
                print(f"Error processing {pptx_file.name}: {str(e)}")
                extracted_data[pptx_file.name] = {"error": str(e)}
        
        # Save combined data
        combined_file = self.output_dir / "all_presentations_extracted.json"
        with open(combined_file, 'w', encoding='utf-8') as f:
            json.dump(extracted_data, f, indent=2, ensure_ascii=False, default=str)
        
        return extracted_data
    
    def extract_pptx_content(self, file_path: str) -> Dict[str, Any]:
        """Extract comprehensive content from a single PPTX file."""
        prs = Presentation(file_path)
        
        extraction_data = {
            "metadata": self._extract_metadata(prs, file_path),
            "slide_master": self._extract_slide_master_info(prs),
            "slides": [],
            "presentation_structure": self._extract_presentation_structure(prs),
            "extraction_timestamp": datetime.now().isoformat()
        }
        
        # Extract each slide
        for slide_idx, slide in enumerate(prs.slides):
            slide_data = self._extract_slide_content(slide, slide_idx)
            extraction_data["slides"].append(slide_data)
        
        return extraction_data
    
    def _extract_metadata(self, prs: Presentation, file_path: str) -> Dict[str, Any]:
        """Extract presentation metadata."""
        core_props = prs.core_properties
        
        metadata = {
            "file_path": file_path,
            "file_size": os.path.getsize(file_path),
            "slide_count": len(prs.slides),
            "slide_width": prs.slide_width,
            "slide_height": prs.slide_height,
            "slide_width_inches": prs.slide_width / 914400,  # Convert EMU to inches
            "slide_height_inches": prs.slide_height / 914400,
            "title": getattr(core_props, 'title', None),
            "author": getattr(core_props, 'author', None),
            "subject": getattr(core_props, 'subject', None),
            "keywords": getattr(core_props, 'keywords', None),
            "comments": getattr(core_props, 'comments', None),
            "category": getattr(core_props, 'category', None),
            "created": getattr(core_props, 'created', None),
            "modified": getattr(core_props, 'modified', None),
            "last_modified_by": getattr(core_props, 'last_modified_by', None),
            "revision": getattr(core_props, 'revision', None),
        }
        
        return metadata
    
    def _extract_slide_master_info(self, prs: Presentation) -> Dict[str, Any]:
        """Extract slide master and layout information."""
        slide_masters = []
        
        for master in prs.slide_masters:
            master_info = {
                "name": getattr(master, 'name', 'Unknown'),
                "layouts": []
            }
            
            for layout in master.slide_layouts:
                layout_info = {
                    "name": getattr(layout, 'name', 'Unknown'),
                    "layout_id": getattr(layout, 'layout_id', None),
                    "placeholders": []
                }
                
                # Extract placeholder information
                for placeholder in layout.placeholders:
                    placeholder_info = {
                        "idx": placeholder.placeholder_format.idx,
                        "type": str(placeholder.placeholder_format.type),
                        "left": placeholder.left,
                        "top": placeholder.top,
                        "width": placeholder.width,
                        "height": placeholder.height,
                    }
                    layout_info["placeholders"].append(placeholder_info)
                
                master_info["layouts"].append(layout_info)
            
            slide_masters.append(master_info)
        
        return {"slide_masters": slide_masters}

    def _extract_presentation_structure(self, prs: Presentation) -> Dict[str, Any]:
        """Extract overall presentation structure information."""
        return {
            "total_slides": len(prs.slides),
            "slide_dimensions": {
                "width_emu": prs.slide_width,
                "height_emu": prs.slide_height,
                "width_inches": prs.slide_width / 914400,
                "height_inches": prs.slide_height / 914400,
            },
            "master_count": len(prs.slide_masters),
            "layout_count": sum(len(master.slide_layouts) for master in prs.slide_masters)
        }

    def _extract_slide_content(self, slide, slide_idx: int) -> Dict[str, Any]:
        """Extract comprehensive content from a single slide."""
        slide_data = {
            "slide_index": slide_idx,
            "slide_id": getattr(slide, 'slide_id', None),
            "layout_name": getattr(slide.slide_layout, 'name', 'Unknown'),
            "shapes": [],
            "notes": self._extract_slide_notes(slide),
            "background": self._extract_slide_background(slide),
            "timing": self._extract_slide_timing(slide)
        }

        # Extract all shapes on the slide
        for shape_idx, shape in enumerate(slide.shapes):
            shape_data = self._extract_shape_content(shape, shape_idx)
            slide_data["shapes"].append(shape_data)

        return slide_data

    def _extract_shape_content(self, shape, shape_idx: int) -> Dict[str, Any]:
        """Extract detailed information from a shape."""
        shape_data = {
            "shape_index": shape_idx,
            "shape_id": getattr(shape, 'shape_id', None),
            "name": getattr(shape, 'name', f'Shape_{shape_idx}'),
            "shape_type": str(shape.shape_type),
            "position": {
                "left": shape.left,
                "top": shape.top,
                "width": shape.width,
                "height": shape.height,
                "left_inches": shape.left / 914400 if shape.left else 0,
                "top_inches": shape.top / 914400 if shape.top else 0,
                "width_inches": shape.width / 914400 if shape.width else 0,
                "height_inches": shape.height / 914400 if shape.height else 0,
            },
            "rotation": getattr(shape, 'rotation', 0),
            "visible": getattr(shape, 'visible', True),
        }

        # Extract content based on shape type
        try:
            if shape.shape_type == MSO_SHAPE_TYPE.TEXT_BOX or hasattr(shape, 'text_frame'):
                shape_data["text_content"] = self._extract_text_content(shape)
        except Exception as e:
            shape_data["text_content"] = {"error": str(e)}

        try:
            if shape.shape_type == MSO_SHAPE_TYPE.PICTURE:
                shape_data["image_content"] = self._extract_image_content(shape)
        except Exception as e:
            shape_data["image_content"] = {"error": str(e)}

        try:
            if shape.shape_type == MSO_SHAPE_TYPE.TABLE:
                shape_data["table_content"] = self._extract_table_content(shape)
        except Exception as e:
            shape_data["table_content"] = {"error": str(e)}

        try:
            if shape.shape_type == MSO_SHAPE_TYPE.CHART:
                shape_data["chart_content"] = self._extract_chart_content(shape)
        except Exception as e:
            shape_data["chart_content"] = {"error": str(e)}

        try:
            if shape.shape_type == MSO_SHAPE_TYPE.GROUP:
                shape_data["group_content"] = self._extract_group_content(shape)
        except Exception as e:
            shape_data["group_content"] = {"error": str(e)}

        # Extract fill and line formatting
        try:
            shape_data["formatting"] = self._extract_shape_formatting(shape)
        except Exception as e:
            shape_data["formatting"] = {"error": str(e)}

        return shape_data

    def _extract_text_content(self, shape) -> Dict[str, Any]:
        """Extract detailed text content and formatting."""
        if not hasattr(shape, 'text_frame'):
            return {}

        text_frame = shape.text_frame
        text_data = {
            "full_text": text_frame.text,
            "paragraphs": [],
            "text_frame_properties": {
                "auto_size": str(getattr(text_frame, 'auto_size', 'NONE')),
                "margin_left": getattr(text_frame, 'margin_left', 0),
                "margin_right": getattr(text_frame, 'margin_right', 0),
                "margin_top": getattr(text_frame, 'margin_top', 0),
                "margin_bottom": getattr(text_frame, 'margin_bottom', 0),
                "word_wrap": getattr(text_frame, 'word_wrap', True),
            }
        }

        # Extract paragraph-level information
        for para_idx, paragraph in enumerate(text_frame.paragraphs):
            para_data = {
                "paragraph_index": para_idx,
                "text": paragraph.text,
                "level": paragraph.level,
                "alignment": str(getattr(paragraph.alignment, 'name', 'UNKNOWN')),
                "runs": []
            }

            # Extract run-level formatting
            for run_idx, run in enumerate(paragraph.runs):
                run_data = {
                    "run_index": run_idx,
                    "text": run.text,
                    "font": self._extract_font_properties(run.font)
                }
                para_data["runs"].append(run_data)

            text_data["paragraphs"].append(para_data)

        return text_data

    def _extract_font_properties(self, font) -> Dict[str, Any]:
        """Extract detailed font formatting properties."""
        font_data = {
            "name": getattr(font, 'name', None),
            "size": getattr(font, 'size', None),
            "bold": getattr(font, 'bold', None),
            "italic": getattr(font, 'italic', None),
            "underline": getattr(font, 'underline', None),
            "color": None,
        }

        # Extract font size in points if available
        if font_data["size"]:
            font_data["size_pt"] = font_data["size"].pt

        # Extract color information
        try:
            if hasattr(font, 'color') and font.color:
                if hasattr(font.color, 'rgb') and font.color.rgb:
                    rgb = font.color.rgb
                    font_data["color"] = {
                        "type": "rgb",
                        "rgb_hex": str(rgb),
                        "red": rgb.red,
                        "green": rgb.green,
                        "blue": rgb.blue
                    }
                elif hasattr(font.color, 'theme_color'):
                    font_data["color"] = {
                        "type": "theme",
                        "theme_color": str(font.color.theme_color)
                    }
        except Exception:
            pass  # Color extraction can be complex, skip if error

        return font_data

    def _extract_image_content(self, shape) -> Dict[str, Any]:
        """Extract image information and optionally encode image data."""
        image_data = {
            "image_type": "picture",
            "crop": {},
            "image_format": None,
            "image_size": None,
        }

        try:
            # Get image binary data
            image_part = shape.image.image_part
            image_data["image_format"] = image_part.content_type
            image_data["image_size"] = len(image_part.blob)

            # Encode image as base64 for storage (optional - can be large)
            # Uncomment the next line if you want to include image data
            # image_data["image_base64"] = base64.b64encode(image_part.blob).decode('utf-8')

            # Extract crop information if available
            if hasattr(shape, 'crop_left'):
                image_data["crop"] = {
                    "left": getattr(shape, 'crop_left', 0),
                    "top": getattr(shape, 'crop_top', 0),
                    "right": getattr(shape, 'crop_right', 0),
                    "bottom": getattr(shape, 'crop_bottom', 0),
                }
        except Exception as e:
            image_data["extraction_error"] = str(e)

        return image_data

    def _extract_table_content(self, shape) -> Dict[str, Any]:
        """Extract table structure and content."""
        if not hasattr(shape, 'table'):
            return {}

        table = shape.table
        table_data = {
            "rows": len(table.rows),
            "columns": len(table.columns),
            "cells": [],
            "column_widths": [col.width for col in table.columns],
            "row_heights": [row.height for row in table.rows],
        }

        # Extract cell content
        for row_idx, row in enumerate(table.rows):
            row_data = []
            for col_idx, cell in enumerate(row.cells):
                cell_data = {
                    "row": row_idx,
                    "column": col_idx,
                    "text": cell.text,
                    "text_frame": self._extract_text_content(cell) if hasattr(cell, 'text_frame') else {},
                    "fill": self._extract_fill_properties(cell.fill) if hasattr(cell, 'fill') else {},
                }
                row_data.append(cell_data)
            table_data["cells"].append(row_data)

        return table_data

    def _extract_chart_content(self, shape) -> Dict[str, Any]:
        """Extract chart information."""
        if not hasattr(shape, 'chart'):
            return {}

        chart = shape.chart
        chart_data = {
            "chart_type": str(chart.chart_type),
            "has_legend": chart.has_legend,
            "chart_title": chart.chart_title.text_frame.text if chart.chart_title else None,
            "categories": [],
            "series": [],
        }

        try:
            # Extract chart data (this can be complex and may not work for all chart types)
            if hasattr(chart, 'plots') and chart.plots:
                plot = chart.plots[0]  # Get first plot

                # Extract categories
                if hasattr(plot, 'categories') and plot.categories:
                    chart_data["categories"] = [cat for cat in plot.categories]

                # Extract series data
                for series_idx, series in enumerate(plot.series):
                    series_data = {
                        "index": series_idx,
                        "name": series.name,
                        "values": list(series.values) if hasattr(series, 'values') else [],
                    }
                    chart_data["series"].append(series_data)
        except Exception as e:
            chart_data["extraction_error"] = str(e)

        return chart_data

    def _extract_group_content(self, shape) -> Dict[str, Any]:
        """Extract grouped shapes content."""
        if not hasattr(shape, 'shapes'):
            return {}

        group_data = {
            "shape_count": len(shape.shapes),
            "grouped_shapes": []
        }

        for idx, grouped_shape in enumerate(shape.shapes):
            grouped_shape_data = self._extract_shape_content(grouped_shape, idx)
            group_data["grouped_shapes"].append(grouped_shape_data)

        return group_data

    def _extract_shape_formatting(self, shape) -> Dict[str, Any]:
        """Extract shape fill, line, and other formatting properties."""
        formatting = {
            "fill": {},
            "line": {},
            "shadow": {},
        }

        # Extract fill properties
        if hasattr(shape, 'fill'):
            formatting["fill"] = self._extract_fill_properties(shape.fill)

        # Extract line properties
        if hasattr(shape, 'line'):
            formatting["line"] = self._extract_line_properties(shape.line)

        # Extract shadow properties
        try:
            if hasattr(shape, 'shadow'):
                formatting["shadow"] = self._extract_shadow_properties(shape.shadow)
        except Exception as e:
            formatting["shadow"] = {"error": f"Shadow extraction failed: {str(e)}"}

        return formatting

    def _extract_fill_properties(self, fill) -> Dict[str, Any]:
        """Extract fill formatting properties."""
        fill_data = {
            "type": str(getattr(fill, 'type', 'UNKNOWN')),
        }

        try:
            if hasattr(fill, 'fore_color') and fill.fore_color:
                if hasattr(fill.fore_color, 'rgb'):
                    rgb = fill.fore_color.rgb
                    fill_data["color"] = {
                        "rgb_hex": str(rgb),
                        "red": rgb.red,
                        "green": rgb.green,
                        "blue": rgb.blue
                    }
        except Exception:
            pass

        return fill_data

    def _extract_line_properties(self, line) -> Dict[str, Any]:
        """Extract line formatting properties."""
        line_data = {
            "width": getattr(line, 'width', None),
            "color": {},
            "dash_style": str(getattr(line, 'dash_style', 'UNKNOWN')),
        }

        try:
            if hasattr(line, 'color') and line.color:
                if hasattr(line.color, 'rgb'):
                    rgb = line.color.rgb
                    line_data["color"] = {
                        "rgb_hex": str(rgb),
                        "red": rgb.red,
                        "green": rgb.green,
                        "blue": rgb.blue
                    }
        except Exception:
            pass

        return line_data

    def _extract_shadow_properties(self, shadow) -> Dict[str, Any]:
        """Extract shadow properties."""
        return {
            "visible": getattr(shadow, 'visible', False),
            # Add more shadow properties as needed
        }

    def _extract_slide_notes(self, slide) -> Dict[str, Any]:
        """Extract slide notes."""
        notes_data = {"has_notes": False, "notes_text": ""}

        try:
            if hasattr(slide, 'notes_slide') and slide.notes_slide:
                notes_slide = slide.notes_slide
                if hasattr(notes_slide, 'notes_text_frame'):
                    notes_data["has_notes"] = True
                    notes_data["notes_text"] = notes_slide.notes_text_frame.text
        except Exception:
            pass

        return notes_data

    def _extract_slide_background(self, slide) -> Dict[str, Any]:
        """Extract slide background information."""
        background_data = {"type": "unknown"}

        try:
            if hasattr(slide, 'background'):
                background = slide.background
                if hasattr(background, 'fill'):
                    background_data = self._extract_fill_properties(background.fill)
        except Exception:
            pass

        return background_data

    def _extract_slide_timing(self, slide) -> Dict[str, Any]:
        """Extract slide timing and transition information."""
        timing_data = {
            "advance_on_click": True,  # Default
            "advance_after_time": None,
            "transition_type": "none",
        }

        # Note: Transition extraction is complex and may require direct XML parsing
        # This is a placeholder for basic timing information

        return timing_data

    def generate_recreation_script(self, extracted_data: Dict[str, Any], output_file: str = "recreate_presentation.py"):
        """Generate a Python script that can recreate the presentation from extracted data."""
        script_content = '''#!/usr/bin/env python3
"""
Auto-generated script to recreate PowerPoint presentation from extracted data.
Generated by PPTXExtractor on {timestamp}
"""

from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.enum.text import PP_ALIGN
from pptx.dml.color import RGBColor
import json

def recreate_presentation(data_file, output_file):
    """Recreate presentation from extracted data."""
    with open(data_file, 'r', encoding='utf-8') as f:
        data = json.load(f)

    # Create new presentation
    prs = Presentation()

    # Set slide dimensions
    metadata = data.get('metadata', {{}})
    if 'slide_width' in metadata and 'slide_height' in metadata:
        prs.slide_width = metadata['slide_width']
        prs.slide_height = metadata['slide_height']

    # Recreate slides
    for slide_data in data.get('slides', []):
        slide_layout = prs.slide_layouts[0]  # Use blank layout
        slide = prs.slides.add_slide(slide_layout)

        # Add shapes to slide
        for shape_data in slide_data.get('shapes', []):
            recreate_shape(slide, shape_data)

    # Save presentation
    prs.save(output_file)
    print(f"Recreated presentation saved as: {{output_file}}")

def recreate_shape(slide, shape_data):
    """Recreate a shape on the slide."""
    # This is a simplified recreation - full implementation would be more complex
    shape_type = shape_data.get('shape_type', '')
    position = shape_data.get('position', {{}})

    if 'text_content' in shape_data:
        # Create text box
        left = Inches(position.get('left_inches', 0))
        top = Inches(position.get('top_inches', 0))
        width = Inches(position.get('width_inches', 1))
        height = Inches(position.get('height_inches', 1))

        textbox = slide.shapes.add_textbox(left, top, width, height)
        text_frame = textbox.text_frame
        text_frame.text = shape_data['text_content'].get('full_text', '')

if __name__ == "__main__":
    import sys
    if len(sys.argv) != 3:
        print("Usage: python recreate_presentation.py <data_file.json> <output.pptx>")
        sys.exit(1)

    recreate_presentation(sys.argv[1], sys.argv[2])
'''.format(timestamp=datetime.now().isoformat())

        script_path = self.output_dir / output_file
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(script_content)

        print(f"Recreation script saved as: {script_path}")
        return script_path


def main():
    """Main execution function."""
    print("PPTX Content Extractor")
    print("=" * 50)

    # Initialize extractor
    extractor = PPTXExtractor()

    # Extract all PPTX files in current directory
    current_dir = os.getcwd()
    print(f"Scanning directory: {current_dir}")

    extracted_data = extractor.extract_all_pptx_files(current_dir)

    if extracted_data:
        print(f"\\nSuccessfully processed {len(extracted_data)} file(s)")
        print(f"Extraction data saved in: {extractor.output_dir}")

        # Generate recreation script for the first successfully processed file
        for filename, data in extracted_data.items():
            if "error" not in data:
                print(f"\\nGenerating recreation script for: {filename}")
                extractor.generate_recreation_script(data, f"recreate_{filename.replace('.pptx', '.py')}")
                break

        # Print summary
        print("\\n" + "=" * 50)
        print("EXTRACTION SUMMARY")
        print("=" * 50)

        for filename, data in extracted_data.items():
            if "error" in data:
                print(f"❌ {filename}: {data['error']}")
            else:
                slide_count = len(data.get('slides', []))
                print(f"✅ {filename}: {slide_count} slides extracted")

                # Print detailed statistics
                total_shapes = sum(len(slide.get('shapes', [])) for slide in data.get('slides', []))
                total_text_shapes = sum(
                    len([s for s in slide.get('shapes', []) if 'text_content' in s])
                    for slide in data.get('slides', [])
                )
                total_images = sum(
                    len([s for s in slide.get('shapes', []) if 'image_content' in s])
                    for slide in data.get('slides', [])
                )

                print(f"   📊 Total shapes: {total_shapes}")
                print(f"   📝 Text shapes: {total_text_shapes}")
                print(f"   🖼️  Images: {total_images}")
    else:
        print("No PPTX files found or processed successfully.")


if __name__ == "__main__":
    main()
