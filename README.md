# PPTX Content Extractor

A comprehensive Python script that extracts all content and structural information from PowerPoint (PPTX) files and converts it into structured data format for analysis or recreation purposes.

## Features

### Content Extraction
- **Text Content**: All text from slides including titles, body text, bullet points, and text boxes
- **Slide Structure**: Slide layouts, master slide information, and slide ordering
- **Visual Elements**: Images, shapes, charts, tables, and their positioning/sizing information
- **Formatting Details**: Font styles, colors, sizes, alignment, and other text formatting
- **Layout Information**: Slide dimensions, element coordinates, and spatial relationships
- **Metadata**: Presentation properties, author information, creation dates, etc.

### Output Formats
- **JSON Files**: Structured data for each presentation and combined data
- **Recreation Scripts**: Auto-generated Python scripts to recreate presentations
- **Detailed Reports**: Comprehensive extraction summaries

## Installation

1. Install required dependencies:
```bash
pip install -r requirements.txt
```

Or install manually:
```bash
pip install python-pptx lxml Pillow
```

## Usage

### Basic Usage
Run the extractor in the directory containing your PPTX files:

```bash
python pptx_extractor.py
```

This will:
1. <PERSON>an the current directory for all `.pptx` files
2. Extract comprehensive content from each file
3. Save structured data as JSON files in `extracted_pptx_data/` directory
4. Generate recreation scripts for successful extractions
5. Display a detailed summary report

### Programmatic Usage

```python
from pptx_extractor import PPTXExtractor

# Initialize extractor
extractor = PPTXExtractor(output_dir="my_output_folder")

# Extract all PPTX files in a directory
extracted_data = extractor.extract_all_pptx_files("/path/to/pptx/files")

# Extract a single file
single_file_data = extractor.extract_pptx_content("presentation.pptx")

# Generate recreation script
extractor.generate_recreation_script(single_file_data, "recreate_script.py")
```

## Output Structure

### JSON Data Structure
The extracted data follows this hierarchical structure:

```json
{
  "metadata": {
    "file_path": "presentation.pptx",
    "slide_count": 10,
    "slide_width": 9144000,
    "slide_height": 6858000,
    "title": "Presentation Title",
    "author": "Author Name",
    "created": "2023-01-01T00:00:00",
    "modified": "2023-01-02T00:00:00"
  },
  "slide_master": {
    "slide_masters": [
      {
        "name": "Master Name",
        "layouts": [
          {
            "name": "Layout Name",
            "placeholders": [...]
          }
        ]
      }
    ]
  },
  "slides": [
    {
      "slide_index": 0,
      "layout_name": "Title Slide",
      "shapes": [
        {
          "shape_type": "TEXT_BOX",
          "position": {
            "left": 1000000,
            "top": 2000000,
            "width": 8000000,
            "height": 1000000,
            "left_inches": 1.09,
            "top_inches": 2.19,
            "width_inches": 8.75,
            "height_inches": 1.09
          },
          "text_content": {
            "full_text": "Slide Title",
            "paragraphs": [
              {
                "text": "Slide Title",
                "level": 0,
                "alignment": "CENTER",
                "runs": [
                  {
                    "text": "Slide Title",
                    "font": {
                      "name": "Arial",
                      "size_pt": 24,
                      "bold": true,
                      "color": {
                        "type": "rgb",
                        "rgb_hex": "FF0000",
                        "red": 255,
                        "green": 0,
                        "blue": 0
                      }
                    }
                  }
                ]
              }
            ]
          }
        }
      ]
    }
  ]
}
```

### File Organization
```
extracted_pptx_data/
├── presentation1_extracted.json      # Individual file data
├── presentation2_extracted.json
├── all_presentations_extracted.json  # Combined data
├── recreate_presentation1.py         # Recreation script
└── recreate_presentation2.py
```

## Extracted Information Details

### Text Content
- Full text content with paragraph and run-level formatting
- Font properties (name, size, bold, italic, underline, color)
- Text alignment and indentation levels
- Text frame properties (margins, word wrap, auto-sizing)

### Visual Elements
- **Images**: Format, size, crop information, optional base64 encoding
- **Tables**: Cell content, formatting, row/column dimensions
- **Charts**: Chart type, data series, categories, legends
- **Shapes**: Geometric shapes with fill, line, and shadow properties

### Layout Information
- Precise positioning in EMU (English Metric Units) and inches
- Shape dimensions and rotation
- Slide master and layout relationships
- Placeholder information

### Formatting
- Fill properties (solid colors, gradients, patterns)
- Line properties (width, color, dash styles)
- Shadow effects
- Color information (RGB, theme colors)

## Recreation Scripts

The extractor automatically generates Python scripts that can recreate presentations from the extracted data:

```bash
python recreate_presentation1.py extracted_data.json output.pptx
```

Note: Recreation scripts provide a basic framework. Complex formatting and advanced features may require manual enhancement.

## Limitations

1. **Complex Charts**: Advanced chart types may not extract completely
2. **Embedded Objects**: OLE objects and embedded files are not fully supported
3. **Animations**: Slide animations and transitions are not extracted
4. **Advanced Formatting**: Some complex formatting may be simplified
5. **Image Data**: Image binary data is optionally included (can be large)

## Error Handling

The extractor includes comprehensive error handling:
- Individual slide/shape extraction errors don't stop the entire process
- Detailed error messages in output data
- Graceful fallbacks for unsupported features

## Contributing

To extend the extractor:
1. Add new extraction methods for specific content types
2. Enhance the recreation script generation
3. Improve error handling and edge cases
4. Add support for additional PowerPoint features

## License

This script is provided as-is for educational and analysis purposes.
