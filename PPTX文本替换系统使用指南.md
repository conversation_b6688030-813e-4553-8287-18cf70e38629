# PPTX文本替换系统使用指南

## 🎯 **系统概述**

这是一个完整的PPTX文本替换系统，能够将PPTX文件转化为代码形式，保持所有视觉元素（形状、颜色、位置、图表等）完全不变，只允许修改文本内容。

## 🔧 **核心功能**

### ✅ **保持不变的内容**
- **所有图形形状** - 圆角矩形、圆环、箭头等
- **所有颜色和渐变** - 背景、填充、线条颜色
- **所有位置和尺寸** - 精确的坐标和大小
- **所有图表和数据** - 柱状图、圆环图、饼图等
- **所有视觉效果** - 阴影、3D效果、透明度
- **所有布局结构** - 幻灯片布局和层次关系

### 🔄 **可修改的内容**
- **所有文本内容** - 标题、正文、标签等
- **文本格式保持** - 字体、大小、颜色等格式保持不变
- **支持批量替换** - 一次性替换多个文本

## 📁 **系统文件结构**

```
PPTX文本替换系统/
├── pptx_text_replacer.py          # 核心文本替换器
├── maotai_text_replacer.py        # 茅台专用替换器
├── pptx_text_data/                # 数据目录
│   ├── *_text_structure.json      # 文本结构文件
│   ├── text_replacement_template.json  # 通用替换模板
│   ├── maotai_text_replacements.json   # 茅台替换映射
│   └── maotai_replacement_summary.json # 替换摘要
└── 生成的PPTX文件
```

## 🚀 **使用流程**

### **步骤1: 提取文本结构**
```python
python pptx_text_replacer.py
```

**功能**：
- 提取PPTX中的所有文本内容
- 生成文本ID到内容的映射
- 保留文本位置和格式信息
- 创建可编辑的替换模板

**输出文件**：
- `*_text_structure.json` - 完整文本结构
- `text_replacement_template.json` - 替换模板

### **步骤2: 自定义文本替换**

#### **方法A: 使用茅台专用替换器**
```python
python maotai_text_replacer.py
```

#### **方法B: 手动编辑替换模板**
编辑 `text_replacement_template.json` 文件：

```json
{
  "text_1": {
    "original_text": "职场可视化逻辑图ppt模板",
    "new_text": "您的新标题",
    "location_info": "幻灯片1 - 形状1236"
  }
}
```

### **步骤3: 应用替换生成新PPT**
系统会自动生成新的PPTX文件，保持所有视觉元素不变。

## 📊 **实际使用效果**

### **茅台集团示例结果**
- **原始文件**: `（黑红）职场工作汇报ppt-1.pptx`
- **提取文本**: 1,800个文本元素
- **成功替换**: 303个文本元素 (16.83%)
- **生成文件**: `茅台集团数字化转型战略汇报_完整版.pptx`

### **替换示例**
| 原始文本 | 替换后文本 | 位置 |
|---------|-----------|------|
| 职场可视化逻辑图ppt模板 | 茅台集团数字化转型战略汇报 | 幻灯片1 |
| 某某某 | 茅台集团 | 幻灯片1 |
| 汇报人 | 汇报单位 | 幻灯片1 |
| 点击输入标题 | 茅台数字化转型 | 多个位置 |

## 🔧 **高级自定义**

### **创建自定义替换器**

```python
from pptx_text_replacer import PPTXTextReplacer

class CustomTextReplacer:
    def __init__(self):
        self.replacer = PPTXTextReplacer()
        
        # 自定义替换映射
        self.custom_replacements = {
            "原始文本1": "新文本1",
            "原始文本2": "新文本2",
            # 添加更多替换...
        }
    
    def apply_custom_replacements(self, original_pptx, output_pptx):
        # 应用自定义替换
        return self.replacer.apply_text_replacements(
            original_pptx, self.custom_replacements, output_pptx
        )
```

### **批量处理多个文件**

```python
import os
from pathlib import Path

def batch_process_pptx_files(input_dir, output_dir, replacements):
    """批量处理多个PPTX文件"""
    replacer = PPTXTextReplacer()
    
    for pptx_file in Path(input_dir).glob("*.pptx"):
        output_file = Path(output_dir) / f"processed_{pptx_file.name}"
        replacer.apply_text_replacements(str(pptx_file), replacements, str(output_file))
        print(f"✅ 处理完成: {output_file}")
```

## 📋 **文本结构数据格式**

### **文本结构JSON格式**
```json
{
  "metadata": {
    "filename": "原始文件名.pptx",
    "slide_count": 81,
    "slide_width": 12192000,
    "slide_height": 6858000
  },
  "text_map": {
    "text_1": "文本内容1",
    "text_2": "文本内容2"
  },
  "slides": [
    {
      "slide_index": 0,
      "text_elements": [
        {
          "text_id": "text_1",
          "text_content": "实际文本内容",
          "location": {
            "slide_index": 0,
            "shape_index": 0,
            "shape_type": "PLACEHOLDER (14)",
            "shape_name": "形状1236"
          },
          "formatting": {
            "font": {
              "name": "微软雅黑",
              "size": 24,
              "bold": true
            }
          }
        }
      ]
    }
  ]
}
```

## 🎨 **支持的文本类型**

### ✅ **完全支持**
- **标题文本** - 幻灯片标题
- **正文文本** - 段落内容
- **形状文本** - 图形中的文本
- **表格文本** - 表格单元格文本
- **组合形状文本** - 组合图形中的文本

### ⚠️ **部分支持**
- **图表标签** - 图表中的数据标签
- **SmartArt文本** - SmartArt图形中的文本

### ❌ **不支持**
- **图片中的文字** - 嵌入图片中的文本
- **艺术字效果** - 特殊艺术字效果

## 🔍 **故障排除**

### **常见问题**

#### **Q: 替换后文本格式发生变化？**
A: 系统保持原有格式不变，如果格式变化，请检查原始PPT中的文本格式设置。

#### **Q: 某些文本没有被替换？**
A: 检查文本是否在替换映射中，或者文本可能在不支持的元素中（如图片、艺术字）。

#### **Q: 生成的PPT文件损坏？**
A: 确保原始PPT文件完整，并检查替换文本中是否包含特殊字符。

### **调试方法**

```python
# 查看提取的文本结构
with open('pptx_text_data/text_structure.json', 'r', encoding='utf-8') as f:
    data = json.load(f)
    print(f"提取到 {len(data['text_map'])} 个文本元素")

# 查看替换摘要
with open('pptx_text_data/replacement_summary.json', 'r', encoding='utf-8') as f:
    summary = json.load(f)
    print(f"替换率: {summary['replacement_summary']['change_percentage']}%")
```

## 🎯 **最佳实践**

### **1. 文本替换策略**
- **精确匹配优先** - 使用完整文本匹配
- **关键词替换** - 对于重复出现的关键词
- **分批替换** - 先替换重要内容，再处理细节

### **2. 质量控制**
- **预览检查** - 替换前先查看文本结构
- **分步验证** - 分批次应用替换并验证
- **备份原文件** - 始终保留原始文件备份

### **3. 性能优化**
- **批量处理** - 一次性处理多个替换
- **缓存结构** - 重复使用文本结构数据
- **增量更新** - 只替换变化的部分

## 🚀 **扩展功能**

### **未来可扩展的功能**
- **多语言支持** - 支持多种语言的文本替换
- **模板库** - 预定义的行业模板
- **AI智能替换** - 基于上下文的智能文本生成
- **版本控制** - 文本替换的版本管理

---

## 📞 **技术支持**

如需技术支持或功能定制，请联系开发团队。

**系统特点**：
- ✅ **完整保留视觉效果** - 所有图形、颜色、布局保持不变
- ✅ **精确文本替换** - 支持1800+文本元素的精确替换
- ✅ **批量处理能力** - 支持大规模文本替换操作
- ✅ **格式保持** - 文本格式和样式完全保留
- ✅ **易于使用** - 简单的Python脚本操作

这个系统完美解决了您的需求：**将PPTX转化为代码形式，保持除文本外的所有内容不变，支持灵活的文本替换！** 🎯✨
