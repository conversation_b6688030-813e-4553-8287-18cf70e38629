#!/usr/bin/env python3
"""
PPTX文本替换器
将PPTX文件转化为代码形式，保持所有视觉元素不变，只允许修改文本内容

功能：
1. 提取PPTX中的所有文本内容，生成可编辑的文本映射
2. 保持所有视觉元素（形状、颜色、位置、图表等）完全不变
3. 支持批量文本替换
4. 重新生成PPTX文件

Author: AI Assistant
Date: 2025-07-14
"""

import os
import json
import copy
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime

from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.enum.shapes import MSO_SHAPE_TYPE

class PPTXTextReplacer:
    """PPTX文本替换器"""
    
    def __init__(self, output_dir: str = "pptx_text_data"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
    def extract_text_structure(self, pptx_file: str) -> Dict[str, Any]:
        """提取PPTX的完整文本结构，保持所有其他元素不变"""
        print(f"📝 开始提取文本结构: {pptx_file}")
        
        prs = Presentation(pptx_file)
        
        # 创建完整的文本结构数据
        text_structure = {
            "metadata": {
                "filename": os.path.basename(pptx_file),
                "slide_count": len(prs.slides),
                "slide_width": prs.slide_width,
                "slide_height": prs.slide_height,
                "extraction_time": datetime.now().isoformat()
            },
            "text_map": {},  # 文本ID到文本内容的映射
            "slides": []
        }
        
        text_id_counter = 1
        
        # 遍历所有幻灯片
        for slide_idx, slide in enumerate(prs.slides):
            slide_data = {
                "slide_index": slide_idx,
                "slide_title": f"幻灯片 {slide_idx + 1}",
                "text_elements": []
            }
            
            # 遍历幻灯片中的所有形状
            for shape_idx, shape in enumerate(slide.shapes):
                text_elements = self._extract_shape_text(shape, slide_idx, shape_idx, text_id_counter)
                
                for text_element in text_elements:
                    text_id = text_element["text_id"]
                    text_content = text_element["text_content"]
                    
                    # 添加到文本映射
                    text_structure["text_map"][text_id] = text_content
                    
                    # 添加到幻灯片数据
                    slide_data["text_elements"].append(text_element)
                    
                    text_id_counter += 1
            
            text_structure["slides"].append(slide_data)
        
        # 保存文本结构
        output_file = self.output_dir / f"{Path(pptx_file).stem}_text_structure.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(text_structure, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"✅ 文本结构提取完成: {output_file}")
        print(f"📊 提取统计: {len(text_structure['text_map'])} 个文本元素")
        
        return text_structure
    
    def _extract_shape_text(self, shape, slide_idx: int, shape_idx: int, text_id_start: int) -> List[Dict[str, Any]]:
        """提取形状中的文本内容"""
        text_elements = []
        
        try:
            # 检查形状是否有文本框
            if hasattr(shape, 'text_frame') and shape.text_frame:
                text_frame = shape.text_frame
                
                # 遍历文本框中的段落
                for para_idx, paragraph in enumerate(text_frame.paragraphs):
                    if paragraph.text.strip():  # 只处理非空文本
                        text_id = f"text_{text_id_start + len(text_elements)}"
                        
                        text_element = {
                            "text_id": text_id,
                            "text_content": paragraph.text,
                            "location": {
                                "slide_index": slide_idx,
                                "shape_index": shape_idx,
                                "paragraph_index": para_idx,
                                "shape_type": str(shape.shape_type),
                                "shape_name": getattr(shape, 'name', f'形状{shape_idx}')
                            },
                            "formatting": self._extract_text_formatting(paragraph),
                            "shape_info": {
                                "left": getattr(shape, 'left', 0),
                                "top": getattr(shape, 'top', 0),
                                "width": getattr(shape, 'width', 0),
                                "height": getattr(shape, 'height', 0)
                            }
                        }
                        
                        text_elements.append(text_element)
            
            # 处理表格中的文本
            if shape.shape_type == MSO_SHAPE_TYPE.TABLE and hasattr(shape, 'table'):
                table_texts = self._extract_table_text(shape.table, slide_idx, shape_idx, text_id_start + len(text_elements))
                text_elements.extend(table_texts)
            
            # 处理组合形状中的文本
            if shape.shape_type == MSO_SHAPE_TYPE.GROUP and hasattr(shape, 'shapes'):
                for grouped_shape in shape.shapes:
                    grouped_texts = self._extract_shape_text(grouped_shape, slide_idx, shape_idx, text_id_start + len(text_elements))
                    text_elements.extend(grouped_texts)
                    
        except Exception as e:
            print(f"⚠️ 提取形状文本时出错: {e}")
        
        return text_elements
    
    def _extract_table_text(self, table, slide_idx: int, shape_idx: int, text_id_start: int) -> List[Dict[str, Any]]:
        """提取表格中的文本"""
        text_elements = []
        
        try:
            for row_idx, row in enumerate(table.rows):
                for col_idx, cell in enumerate(row.cells):
                    if cell.text.strip():
                        text_id = f"text_{text_id_start + len(text_elements)}"
                        
                        text_element = {
                            "text_id": text_id,
                            "text_content": cell.text,
                            "location": {
                                "slide_index": slide_idx,
                                "shape_index": shape_idx,
                                "table_row": row_idx,
                                "table_col": col_idx,
                                "shape_type": "TABLE_CELL"
                            },
                            "formatting": self._extract_text_formatting(cell.text_frame.paragraphs[0]) if cell.text_frame.paragraphs else {},
                            "table_info": {
                                "row_index": row_idx,
                                "col_index": col_idx,
                                "cell_width": cell.width,
                                "cell_height": cell.height
                            }
                        }
                        
                        text_elements.append(text_element)
                        
        except Exception as e:
            print(f"⚠️ 提取表格文本时出错: {e}")
        
        return text_elements
    
    def _extract_text_formatting(self, paragraph) -> Dict[str, Any]:
        """提取文本格式信息"""
        formatting = {}
        
        try:
            # 段落级格式
            formatting["alignment"] = str(getattr(paragraph, 'alignment', 'LEFT'))
            formatting["level"] = getattr(paragraph, 'level', 0)
            
            # 字体格式（取第一个run的格式）
            if paragraph.runs:
                run = paragraph.runs[0]
                font = run.font
                
                formatting["font"] = {
                    "name": getattr(font, 'name', None),
                    "size": getattr(font, 'size', None),
                    "bold": getattr(font, 'bold', None),
                    "italic": getattr(font, 'italic', None),
                    "underline": str(getattr(font, 'underline', None)),
                    "color": self._extract_font_color(font)
                }
                
        except Exception as e:
            formatting["extraction_error"] = str(e)
        
        return formatting
    
    def _extract_font_color(self, font) -> Dict[str, Any]:
        """提取字体颜色信息"""
        color_info = {"type": "none"}
        
        try:
            if hasattr(font, 'color') and font.color:
                color = font.color
                color_info["type"] = str(getattr(color, 'type', 'UNKNOWN'))
                
                if hasattr(color, 'rgb') and color.rgb:
                    rgb = color.rgb
                    color_info["rgb"] = {
                        "hex": str(rgb),
                        "red": rgb.red,
                        "green": rgb.green,
                        "blue": rgb.blue
                    }
                    
        except Exception as e:
            color_info["extraction_error"] = str(e)
        
        return color_info
    
    def create_text_replacement_template(self, text_structure: Dict[str, Any]) -> Dict[str, str]:
        """创建文本替换模板"""
        template = {}
        
        for text_id, text_content in text_structure["text_map"].items():
            # 创建带注释的模板
            template[text_id] = {
                "original_text": text_content,
                "new_text": text_content,  # 默认保持原文本
                "location_info": self._get_text_location_info(text_id, text_structure)
            }
        
        # 保存模板
        template_file = self.output_dir / "text_replacement_template.json"
        with open(template_file, 'w', encoding='utf-8') as f:
            json.dump(template, f, indent=2, ensure_ascii=False)
        
        print(f"📝 文本替换模板已创建: {template_file}")
        return template
    
    def _get_text_location_info(self, text_id: str, text_structure: Dict[str, Any]) -> str:
        """获取文本位置信息"""
        for slide in text_structure["slides"]:
            for text_element in slide["text_elements"]:
                if text_element["text_id"] == text_id:
                    location = text_element["location"]
                    return f"幻灯片{location['slide_index']+1} - {location['shape_name']}"
        return "未知位置"
    
    def apply_text_replacements(self, original_pptx: str, text_replacements: Dict[str, str], output_pptx: str):
        """应用文本替换，生成新的PPTX文件"""
        print(f"🔄 开始应用文本替换...")
        
        # 加载原始演示文稿
        prs = Presentation(original_pptx)
        
        # 统计替换数量
        replacement_count = 0
        
        # 遍历所有幻灯片和形状
        for slide_idx, slide in enumerate(prs.slides):
            for shape_idx, shape in enumerate(slide.shapes):
                replacement_count += self._replace_shape_text(shape, text_replacements, slide_idx, shape_idx)
        
        # 保存新的演示文稿
        prs.save(output_pptx)
        
        print(f"✅ 文本替换完成: {output_pptx}")
        print(f"📊 替换统计: {replacement_count} 处文本被替换")
        
        return replacement_count
    
    def _replace_shape_text(self, shape, text_replacements: Dict[str, str], slide_idx: int, shape_idx: int) -> int:
        """替换形状中的文本"""
        replacement_count = 0
        
        try:
            # 处理文本框
            if hasattr(shape, 'text_frame') and shape.text_frame:
                text_frame = shape.text_frame
                
                for para_idx, paragraph in enumerate(text_frame.paragraphs):
                    original_text = paragraph.text
                    
                    # 查找匹配的替换
                    for new_text in text_replacements.values():
                        if isinstance(new_text, dict) and new_text.get("original_text") == original_text:
                            paragraph.text = new_text.get("new_text", original_text)
                            replacement_count += 1
                            break
                        elif isinstance(new_text, str) and original_text in text_replacements:
                            paragraph.text = new_text
                            replacement_count += 1
                            break
            
            # 处理表格
            if shape.shape_type == MSO_SHAPE_TYPE.TABLE and hasattr(shape, 'table'):
                replacement_count += self._replace_table_text(shape.table, text_replacements)
            
            # 处理组合形状
            if shape.shape_type == MSO_SHAPE_TYPE.GROUP and hasattr(shape, 'shapes'):
                for grouped_shape in shape.shapes:
                    replacement_count += self._replace_shape_text(grouped_shape, text_replacements, slide_idx, shape_idx)
                    
        except Exception as e:
            print(f"⚠️ 替换形状文本时出错: {e}")
        
        return replacement_count
    
    def _replace_table_text(self, table, text_replacements: Dict[str, str]) -> int:
        """替换表格中的文本"""
        replacement_count = 0
        
        try:
            for row in table.rows:
                for cell in row.cells:
                    original_text = cell.text
                    
                    # 查找匹配的替换
                    for new_text in text_replacements.values():
                        if isinstance(new_text, dict) and new_text.get("original_text") == original_text:
                            cell.text = new_text.get("new_text", original_text)
                            replacement_count += 1
                            break
                        elif isinstance(new_text, str) and original_text in text_replacements:
                            cell.text = new_text
                            replacement_count += 1
                            break
                            
        except Exception as e:
            print(f"⚠️ 替换表格文本时出错: {e}")
        
        return replacement_count


def main():
    """主函数 - 演示完整的文本替换流程"""
    replacer = PPTXTextReplacer()
    
    # 原始PPTX文件
    original_file = "（黑红）职场工作汇报ppt-1.pptx"
    
    if not os.path.exists(original_file):
        print(f"❌ 文件不存在: {original_file}")
        return
    
    print("🚀 开始PPTX文本替换流程...")
    
    # 步骤1: 提取文本结构
    text_structure = replacer.extract_text_structure(original_file)
    
    # 步骤2: 创建文本替换模板
    template = replacer.create_text_replacement_template(text_structure)
    
    # 步骤3: 示例文本替换
    sample_replacements = {
        "text_1": {
            "original_text": "职场工作汇报",
            "new_text": "茅台集团数字化转型汇报"
        },
        "text_2": {
            "original_text": "2024年度总结",
            "new_text": "2024年度茅台发展总结"
        }
    }
    
    # 步骤4: 应用替换并生成新文件
    output_file = "茅台集团_文本替换版.pptx"
    replacer.apply_text_replacements(original_file, sample_replacements, output_file)
    
    print("✅ PPTX文本替换流程完成!")
    print(f"📁 生成的文件:")
    print(f"   - 文本结构: pptx_text_data/{Path(original_file).stem}_text_structure.json")
    print(f"   - 替换模板: pptx_text_data/text_replacement_template.json")
    print(f"   - 新PPTX文件: {output_file}")


if __name__ == "__main__":
    main()
