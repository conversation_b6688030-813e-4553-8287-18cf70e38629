#!/usr/bin/env python3
"""
图表重建器 - 专门重建复杂图表，特别是圆环图
解决PPT复刻中图表缺失的问题

Author: AI Assistant
Date: 2025-07-14
"""

import os
import json
from pathlib import Path
from typing import Dict, List, Any, Tuple

from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.enum.chart import XL_CHART_TYPE, XL_LEGEND_POSITION
from pptx.chart.data import CategoryChartData
from pptx.dml.color import RGBColor
from pptx.enum.shapes import MSO_SHAPE_TYPE

class ChartRebuilder:
    """图表重建器"""
    
    def __init__(self):
        self.maotai_colors = {
            "primary_red": RGBColor(196, 22, 28),      # 茅台红
            "secondary_red": RGBColor(165, 18, 23),    # 深茅台红
            "accent_gold": RGBColor(218, 165, 32),     # 茅台金
            "neutral_gray": RGBColor(128, 128, 128),   # 中性灰
            "light_gray": RGBColor(192, 192, 192),     # 浅灰
            "dark_gray": RGBColor(64, 64, 64)          # 深灰
        }
    
    def rebuild_charts_from_data(self, advanced_data_file: str, output_pptx: str):
        """从高级数据重建图表"""
        print(f"📊 开始从高级数据重建图表...")
        
        # 加载高级视觉数据
        with open(advanced_data_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 创建新的演示文稿
        prs = Presentation()
        
        # 设置幻灯片尺寸
        prs.slide_width = data["metadata"]["slide_width"]
        prs.slide_height = data["metadata"]["slide_height"]
        
        # 重建包含图表的幻灯片
        chart_data = data.get("chart_detailed_data", {})
        charts = chart_data.get("charts", [])
        
        print(f"🔍 发现 {len(charts)} 个图表需要重建")
        
        for chart_info in charts:
            slide = self._create_chart_slide(prs, chart_info)
            print(f"✅ 重建图表: 幻灯片 {chart_info['slide_index']}, 类型 {chart_info['chart_type']}")
        
        # 添加示例圆环图
        self._create_sample_donut_charts(prs)
        
        # 保存演示文稿
        prs.save(output_pptx)
        print(f"💾 图表重建完成: {output_pptx}")
        
        return len(charts)
    
    def _create_chart_slide(self, prs: Presentation, chart_info: Dict[str, Any]):
        """创建包含图表的幻灯片"""
        slide_layout = prs.slide_layouts[5]  # 空白布局
        slide = prs.slides.add_slide(slide_layout)
        
        # 添加标题
        title_box = slide.shapes.add_textbox(
            Inches(0.5), Inches(0.2), Inches(12), Inches(0.8)
        )
        title_frame = title_box.text_frame
        title_frame.text = f"重建图表 - 幻灯片 {chart_info['slide_index']}"
        
        # 根据图表类型创建相应图表
        chart_type = chart_info.get("chart_type", "")
        
        if "COLUMN" in chart_type:
            self._create_column_chart(slide, chart_info)
        elif "DONUT" in chart_type or "PIE" in chart_type:
            self._create_donut_chart(slide, chart_info)
        elif "LINE" in chart_type:
            self._create_line_chart(slide, chart_info)
        else:
            self._create_default_chart(slide, chart_info)
        
        return slide
    
    def _create_column_chart(self, slide, chart_info: Dict[str, Any]):
        """创建柱状图"""
        # 准备图表数据
        chart_data = CategoryChartData()
        
        data_info = chart_info.get("data", {})
        categories = data_info.get("categories", ["类别1", "类别2", "类别3"])
        series_list = data_info.get("series", [])
        
        # 添加类别
        chart_data.categories = categories
        
        # 添加数据系列
        for i, series in enumerate(series_list):
            series_name = series.get("name", f"系列{i+1}")
            values = series.get("values", [10, 20, 30])
            chart_data.add_series(series_name, values)
        
        # 创建图表
        position = chart_info.get("position", {})
        left = Inches(position.get("left", 914400) / 914400)
        top = Inches(position.get("top", 1828800) / 914400)
        width = Inches(position.get("width", 5486400) / 914400)
        height = Inches(position.get("height", 3657600) / 914400)
        
        chart = slide.shapes.add_chart(
            XL_CHART_TYPE.COLUMN_CLUSTERED, left, top, width, height, chart_data
        ).chart
        
        # 应用茅台配色
        self._apply_maotai_colors_to_chart(chart)
        
        return chart
    
    def _create_donut_chart(self, slide, chart_info: Dict[str, Any]):
        """创建圆环图"""
        # 准备圆环图数据
        chart_data = CategoryChartData()
        
        # 使用示例数据或从chart_info提取
        data_info = chart_info.get("data", {})
        categories = data_info.get("categories", ["电商平台利润", "线下预期", "多领域收入", "持续优势"])
        
        # 如果有系列数据，使用第一个系列
        series_list = data_info.get("series", [])
        if series_list:
            values = series_list[0].get("values", [40, 25, 20, 15])
        else:
            values = [40, 25, 20, 15]  # 默认数据
        
        chart_data.categories = categories
        chart_data.add_series("利润分布", values)
        
        # 创建圆环图
        position = chart_info.get("position", {})
        left = Inches(position.get("left", 914400) / 914400)
        top = Inches(position.get("top", 1828800) / 914400)
        width = Inches(position.get("width", 4572000) / 914400)
        height = Inches(position.get("height", 4572000) / 914400)
        
        chart = slide.shapes.add_chart(
            XL_CHART_TYPE.DOUGHNUT, left, top, width, height, chart_data
        ).chart
        
        # 设置圆环图特有属性
        chart.has_legend = True
        chart.legend.position = XL_LEGEND_POSITION.RIGHT
        
        # 应用茅台配色方案
        self._apply_donut_maotai_colors(chart)
        
        return chart
    
    def _create_line_chart(self, slide, chart_info: Dict[str, Any]):
        """创建折线图"""
        chart_data = CategoryChartData()
        
        data_info = chart_info.get("data", {})
        categories = data_info.get("categories", ["Q1", "Q2", "Q3", "Q4"])
        series_list = data_info.get("series", [])
        
        chart_data.categories = categories
        
        for i, series in enumerate(series_list):
            series_name = series.get("name", f"趋势{i+1}")
            values = series.get("values", [100, 120, 110, 140])
            chart_data.add_series(series_name, values)
        
        # 创建折线图
        position = chart_info.get("position", {})
        left = Inches(position.get("left", 914400) / 914400)
        top = Inches(position.get("top", 1828800) / 914400)
        width = Inches(position.get("width", 5486400) / 914400)
        height = Inches(position.get("height", 3657600) / 914400)
        
        chart = slide.shapes.add_chart(
            XL_CHART_TYPE.LINE, left, top, width, height, chart_data
        ).chart
        
        self._apply_maotai_colors_to_chart(chart)
        
        return chart
    
    def _create_default_chart(self, slide, chart_info: Dict[str, Any]):
        """创建默认图表（柱状图）"""
        return self._create_column_chart(slide, chart_info)
    
    def _create_sample_donut_charts(self, prs: Presentation):
        """创建示例圆环图展示"""
        slide_layout = prs.slide_layouts[5]
        slide = prs.slides.add_slide(slide_layout)
        
        # 添加标题
        title_box = slide.shapes.add_textbox(Inches(0.5), Inches(0.2), Inches(12), Inches(0.8))
        title_frame = title_box.text_frame
        title_frame.text = "茅台集团数字化转型 - 利润分布圆环图"
        title_frame.paragraphs[0].font.size = Pt(24)
        title_frame.paragraphs[0].font.bold = True
        
        # 创建主要圆环图
        self._create_main_donut_chart(slide)
        
        # 创建对比圆环图
        self._create_comparison_donut_chart(slide)
        
        return slide
    
    def _create_main_donut_chart(self, slide):
        """创建主要圆环图"""
        # 2023年数据
        chart_data = CategoryChartData()
        chart_data.categories = ['电商平台利润', '线下预期收入', '多领域收入增量', '持续优势项目']
        chart_data.add_series('2023年利润分布', [35, 28, 22, 15])
        
        # 创建圆环图
        chart = slide.shapes.add_chart(
            XL_CHART_TYPE.DOUGHNUT, 
            Inches(1), Inches(1.5), Inches(5), Inches(4), chart_data
        ).chart
        
        # 设置图表属性
        chart.has_legend = True
        chart.legend.position = XL_LEGEND_POSITION.RIGHT
        chart.legend.font.size = Pt(12)
        
        # 应用茅台配色
        self._apply_donut_maotai_colors(chart)
        
        # 添加数据标签
        plot = chart.plots[0]
        plot.has_data_labels = True
        data_labels = plot.data_labels
        data_labels.show_percentage = True
        data_labels.show_category_name = False
        
        return chart
    
    def _create_comparison_donut_chart(self, slide):
        """创建对比圆环图"""
        # 2024年预期数据
        chart_data = CategoryChartData()
        chart_data.categories = ['电商平台销售', '实现路径', '发挥产品优势', '开展职工培训']
        chart_data.add_series('2024年预期', [66, 20, 10, 4])
        
        # 创建圆环图
        chart = slide.shapes.add_chart(
            XL_CHART_TYPE.DOUGHNUT,
            Inches(7), Inches(1.5), Inches(4.5), Inches(4), chart_data
        ).chart
        
        # 设置图表属性
        chart.has_legend = True
        chart.legend.position = XL_LEGEND_POSITION.BOTTOM
        chart.legend.font.size = Pt(10)
        
        # 应用茅台配色
        self._apply_donut_maotai_colors(chart)
        
        # 添加数据标签
        plot = chart.plots[0]
        plot.has_data_labels = True
        data_labels = plot.data_labels
        data_labels.show_percentage = True
        data_labels.show_value = False
        
        return chart
    
    def _apply_maotai_colors_to_chart(self, chart):
        """为图表应用茅台配色方案"""
        try:
            plot = chart.plots[0]
            colors = [
                self.maotai_colors["primary_red"],
                self.maotai_colors["secondary_red"], 
                self.maotai_colors["accent_gold"],
                self.maotai_colors["neutral_gray"],
                self.maotai_colors["light_gray"],
                self.maotai_colors["dark_gray"]
            ]
            
            for i, series in enumerate(plot.series):
                if i < len(colors):
                    # 设置系列颜色
                    fill = series.format.fill
                    fill.solid()
                    fill.fore_color.rgb = colors[i]
                    
        except Exception as e:
            print(f"⚠️ 应用配色时出错: {e}")
    
    def _apply_donut_maotai_colors(self, chart):
        """为圆环图应用茅台配色方案"""
        try:
            plot = chart.plots[0]
            
            # 圆环图的配色方案
            donut_colors = [
                self.maotai_colors["primary_red"],      # 主要部分用茅台红
                self.maotai_colors["secondary_red"],    # 次要部分用深茅台红
                self.maotai_colors["accent_gold"],      # 重点部分用茅台金
                self.maotai_colors["neutral_gray"]      # 其他部分用灰色
            ]
            
            # 应用颜色到各个扇形
            series = plot.series[0]
            for i, point in enumerate(series.points):
                if i < len(donut_colors):
                    fill = point.format.fill
                    fill.solid()
                    fill.fore_color.rgb = donut_colors[i]
                    
        except Exception as e:
            print(f"⚠️ 应用圆环图配色时出错: {e}")


def main():
    """主函数"""
    rebuilder = ChartRebuilder()
    
    # 高级视觉数据文件
    advanced_data_file = "advanced_visual_data/（黑红）职场工作汇报ppt-1_advanced_visuals.json"
    output_file = "茅台集团_图表重建版.pptx"
    
    if os.path.exists(advanced_data_file):
        print(f"📊 开始图表重建...")
        chart_count = rebuilder.rebuild_charts_from_data(advanced_data_file, output_file)
        print(f"✅ 图表重建完成!")
        print(f"   重建图表数量: {chart_count}")
        print(f"   输出文件: {output_file}")
    else:
        print(f"❌ 高级视觉数据文件不存在: {advanced_data_file}")
        print("请先运行 advanced_visual_extractor.py 生成数据文件")


if __name__ == "__main__":
    main()
