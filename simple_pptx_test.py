#!/usr/bin/env python3
"""
Simple test script to verify python-pptx installation and basic functionality.
"""

import sys
import os

def test_import():
    """Test if we can import python-pptx."""
    try:
        import pptx
        print("✅ python-pptx imported successfully")
        print(f"   Version: {pptx.__version__}")
        return True
    except ImportError as e:
        print(f"❌ Failed to import python-pptx: {e}")
        return False

def test_basic_functionality():
    """Test basic functionality."""
    try:
        from pptx import Presentation
        from pptx.enum.shapes import MSO_SHAPE_TYPE
        
        # Find PPTX files in current directory
        pptx_files = [f for f in os.listdir('.') if f.endswith('.pptx')]
        
        if not pptx_files:
            print("❌ No PPTX files found in current directory")
            return False
        
        print(f"📁 Found PPTX files: {pptx_files}")
        
        # Try to open the first file
        test_file = pptx_files[0]
        print(f"🔍 Testing with file: {test_file}")
        
        prs = Presentation(test_file)
        print(f"✅ Successfully opened presentation")
        print(f"   Slides: {len(prs.slides)}")
        print(f"   Slide size: {prs.slide_width} x {prs.slide_height}")
        
        # Test extracting basic info from first slide
        if prs.slides:
            slide = prs.slides[0]
            print(f"   First slide shapes: {len(slide.shapes)}")
            
            for i, shape in enumerate(slide.shapes):
                print(f"     Shape {i}: {shape.shape_type}")
                if hasattr(shape, 'text'):
                    text_preview = shape.text[:50] + "..." if len(shape.text) > 50 else shape.text
                    print(f"       Text: {repr(text_preview)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during basic functionality test: {e}")
        return False

def main():
    """Main test function."""
    print("PPTX Library Test")
    print("=" * 40)
    print(f"Python version: {sys.version}")
    print(f"Current directory: {os.getcwd()}")
    print()
    
    # Test import
    if not test_import():
        print("\n❌ Cannot proceed without python-pptx library")
        return
    
    print()
    
    # Test basic functionality
    if test_basic_functionality():
        print("\n✅ All tests passed! The library is working correctly.")
    else:
        print("\n❌ Some tests failed.")

if __name__ == "__main__":
    main()
