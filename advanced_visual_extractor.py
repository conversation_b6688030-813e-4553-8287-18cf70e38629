#!/usr/bin/env python3
"""
高级视觉元素提取器
专门提取复杂图形、渐变、3D效果、图表等高级视觉元素

Author: AI Assistant
Date: 2025-07-14
"""

import os
import json
import zipfile
import xml.etree.ElementTree as ET
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional

from pptx import Presentation
from pptx.enum.shapes import MSO_SHAPE_TYPE
from pptx.util import Inches, Pt
from pptx.dml.color import RGBColor
from pptx.enum.chart import XL_CHART_TYPE

class AdvancedVisualExtractor:
    """高级视觉元素提取器"""
    
    def __init__(self, output_dir: str = "advanced_visual_data"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
    def extract_advanced_visuals(self, pptx_file: str):
        """提取高级视觉元素"""
        print(f"🎨 开始提取高级视觉元素: {pptx_file}")
        
        # 多层次提取
        pptx_data = self._extract_from_pptx_advanced(pptx_file)
        xml_data = self._extract_xml_advanced(pptx_file)
        chart_data = self._extract_charts_detailed(pptx_file)
        gradient_data = self._extract_gradients_detailed(pptx_file)
        
        complete_data = {
            "metadata": pptx_data["metadata"],
            "pptx_advanced_data": pptx_data,
            "xml_visual_data": xml_data,
            "chart_detailed_data": chart_data,
            "gradient_detailed_data": gradient_data,
            "extraction_timestamp": datetime.now().isoformat()
        }
        
        # 保存数据
        output_file = self.output_dir / f"{Path(pptx_file).stem}_advanced_visuals.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(complete_data, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"✅ 高级视觉元素提取完成: {output_file}")
        return complete_data
    
    def _extract_from_pptx_advanced(self, pptx_file: str) -> Dict[str, Any]:
        """使用python-pptx提取高级视觉信息"""
        prs = Presentation(pptx_file)
        
        data = {
            "metadata": self._extract_metadata(prs, pptx_file),
            "slides": []
        }
        
        for slide_idx, slide in enumerate(prs.slides):
            slide_data = {
                "slide_index": slide_idx,
                "background": self._extract_slide_background_advanced(slide),
                "shapes": []
            }
            
            for shape_idx, shape in enumerate(slide.shapes):
                shape_data = self._extract_shape_advanced(shape, shape_idx)
                slide_data["shapes"].append(shape_data)
            
            data["slides"].append(slide_data)
        
        return data
    
    def _extract_shape_advanced(self, shape, shape_idx: int) -> Dict[str, Any]:
        """提取形状的高级视觉信息"""
        shape_data = {
            "shape_index": shape_idx,
            "shape_type": str(shape.shape_type),
            "basic_info": self._extract_basic_info(shape),
            "advanced_fill": self._extract_advanced_fill(shape),
            "advanced_line": self._extract_advanced_line(shape),
            "shadow_3d": self._extract_shadow_3d_effects(shape),
            "chart_details": self._extract_chart_details(shape),
            "group_details": self._extract_group_details(shape),
            "connector_details": self._extract_connector_details(shape)
        }
        
        return shape_data
    
    def _extract_basic_info(self, shape) -> Dict[str, Any]:
        """提取基本信息"""
        return {
            "left": getattr(shape, 'left', 0),
            "top": getattr(shape, 'top', 0),
            "width": getattr(shape, 'width', 0),
            "height": getattr(shape, 'height', 0),
            "rotation": getattr(shape, 'rotation', 0),
            "z_order": getattr(shape, 'z_order', 0),
            "name": getattr(shape, 'name', ''),
            "shape_id": getattr(shape, 'shape_id', None)
        }
    
    def _extract_advanced_fill(self, shape) -> Dict[str, Any]:
        """提取高级填充信息"""
        fill_data = {"type": "none"}
        
        try:
            if hasattr(shape, 'fill'):
                fill = shape.fill
                fill_data["type"] = str(getattr(fill, 'type', 'UNKNOWN'))
                
                # 渐变填充
                if hasattr(fill, 'gradient') and fill.gradient:
                    fill_data["gradient"] = self._extract_gradient_detailed(fill.gradient)
                
                # 图案填充
                if hasattr(fill, 'pattern'):
                    fill_data["pattern"] = {
                        "pattern_type": str(getattr(fill, 'pattern', 'UNKNOWN')),
                        "fore_color": self._extract_color_detailed(getattr(fill, 'fore_color', None)),
                        "back_color": self._extract_color_detailed(getattr(fill, 'back_color', None))
                    }
                
                # 纹理填充
                if hasattr(fill, 'texture'):
                    fill_data["texture"] = str(getattr(fill, 'texture', 'UNKNOWN'))
                
                # 图片填充
                if hasattr(fill, 'picture'):
                    fill_data["picture"] = {"has_picture_fill": True}
                
                # 透明度
                if hasattr(fill, 'transparency'):
                    fill_data["transparency"] = getattr(fill, 'transparency', 0)
                    
        except Exception as e:
            fill_data["extraction_error"] = str(e)
        
        return fill_data
    
    def _extract_gradient_detailed(self, gradient) -> Dict[str, Any]:
        """提取详细的渐变信息"""
        gradient_data = {}
        
        try:
            gradient_data["angle"] = getattr(gradient, 'angle', 0)
            gradient_data["type"] = str(getattr(gradient, 'type', 'LINEAR'))
            
            # 渐变停止点
            if hasattr(gradient, 'gradient_stops'):
                gradient_data["stops"] = []
                for stop in gradient.gradient_stops:
                    stop_data = {
                        "position": getattr(stop, 'position', 0),
                        "color": self._extract_color_detailed(getattr(stop, 'color', None))
                    }
                    gradient_data["stops"].append(stop_data)
                    
        except Exception as e:
            gradient_data["extraction_error"] = str(e)
        
        return gradient_data
    
    def _extract_color_detailed(self, color) -> Dict[str, Any]:
        """提取详细的颜色信息"""
        if not color:
            return {"type": "none"}
        
        color_data = {}
        
        try:
            color_data["type"] = str(getattr(color, 'type', 'UNKNOWN'))
            
            # RGB颜色
            if hasattr(color, 'rgb'):
                rgb = color.rgb
                color_data["rgb"] = {
                    "hex": str(rgb),
                    "red": rgb.red,
                    "green": rgb.green,
                    "blue": rgb.blue
                }
            
            # 主题颜色
            if hasattr(color, 'theme_color'):
                color_data["theme_color"] = str(getattr(color, 'theme_color', 'UNKNOWN'))
            
            # 亮度调整
            if hasattr(color, 'brightness'):
                color_data["brightness"] = getattr(color, 'brightness', 0)
                
        except Exception as e:
            color_data["extraction_error"] = str(e)
        
        return color_data
    
    def _extract_advanced_line(self, shape) -> Dict[str, Any]:
        """提取高级线条信息"""
        line_data = {"type": "none"}
        
        try:
            if hasattr(shape, 'line'):
                line = shape.line
                
                line_data["color"] = self._extract_color_detailed(getattr(line, 'color', None))
                line_data["width"] = getattr(line, 'width', 0)
                line_data["dash_style"] = str(getattr(line, 'dash_style', 'SOLID'))
                line_data["join_style"] = str(getattr(line, 'join_style', 'ROUND'))
                
                # 箭头信息
                line_data["begin_arrowhead"] = {
                    "style": str(getattr(line, 'begin_arrowhead_style', 'NONE')),
                    "width": str(getattr(line, 'begin_arrowhead_width', 'MEDIUM')),
                    "length": str(getattr(line, 'begin_arrowhead_length', 'MEDIUM'))
                }
                
                line_data["end_arrowhead"] = {
                    "style": str(getattr(line, 'end_arrowhead_style', 'NONE')),
                    "width": str(getattr(line, 'end_arrowhead_width', 'MEDIUM')),
                    "length": str(getattr(line, 'end_arrowhead_length', 'MEDIUM'))
                }
                
        except Exception as e:
            line_data["extraction_error"] = str(e)
        
        return line_data
    
    def _extract_shadow_3d_effects(self, shape) -> Dict[str, Any]:
        """提取阴影和3D效果"""
        effects_data = {}
        
        try:
            # 阴影效果
            if hasattr(shape, 'shadow'):
                shadow = shape.shadow
                effects_data["shadow"] = {
                    "visible": getattr(shadow, 'visible', False),
                    "style": str(getattr(shadow, 'style', 'UNKNOWN')),
                    "blur_radius": getattr(shadow, 'blur_radius', 0),
                    "distance": getattr(shadow, 'distance', 0),
                    "direction": getattr(shadow, 'direction', 0),
                    "color": self._extract_color_detailed(getattr(shadow, 'color', None))
                }
            
            # 3D效果
            if hasattr(shape, 'three_d'):
                three_d = shape.three_d
                effects_data["three_d"] = {
                    "bevel_top_type": str(getattr(three_d, 'bevel_top_type', 'NONE')),
                    "bevel_top_width": getattr(three_d, 'bevel_top_width', 0),
                    "bevel_top_height": getattr(three_d, 'bevel_top_height', 0),
                    "material": str(getattr(three_d, 'material', 'MATTE')),
                    "lighting": str(getattr(three_d, 'lighting', 'THREE_POINT')),
                    "lighting_direction": str(getattr(three_d, 'lighting_direction', 'TOP_LEFT'))
                }
                
        except Exception as e:
            effects_data["extraction_error"] = str(e)
        
        return effects_data
    
    def _extract_chart_details(self, shape) -> Dict[str, Any]:
        """提取详细的图表信息"""
        chart_data = {"has_chart": False}
        
        try:
            if shape.shape_type == MSO_SHAPE_TYPE.CHART and hasattr(shape, 'chart'):
                chart = shape.chart
                chart_data["has_chart"] = True
                chart_data["chart_type"] = str(chart.chart_type)
                chart_data["has_legend"] = getattr(chart, 'has_legend', False)
                
                # 图表标题
                if hasattr(chart, 'chart_title') and chart.chart_title:
                    chart_data["title"] = chart.chart_title.text_frame.text
                
                # 数据系列
                chart_data["series"] = []
                if hasattr(chart, 'plots') and chart.plots:
                    plot = chart.plots[0]
                    for series_idx, series in enumerate(plot.series):
                        series_data = {
                            "index": series_idx,
                            "name": getattr(series, 'name', f'Series {series_idx}'),
                            "values": list(getattr(series, 'values', [])),
                            "categories": list(getattr(plot, 'categories', []))
                        }
                        chart_data["series"].append(series_data)
                        
        except Exception as e:
            chart_data["extraction_error"] = str(e)
        
        return chart_data
    
    def _extract_group_details(self, shape) -> Dict[str, Any]:
        """提取组合形状的详细信息"""
        group_data = {"is_group": False}
        
        try:
            if shape.shape_type == MSO_SHAPE_TYPE.GROUP and hasattr(shape, 'shapes'):
                group_data["is_group"] = True
                group_data["shape_count"] = len(shape.shapes)
                group_data["grouped_shapes"] = []
                
                for grouped_shape in shape.shapes:
                    grouped_data = self._extract_shape_advanced(grouped_shape, 0)
                    group_data["grouped_shapes"].append(grouped_data)
                    
        except Exception as e:
            group_data["extraction_error"] = str(e)
        
        return group_data
    
    def _extract_connector_details(self, shape) -> Dict[str, Any]:
        """提取连接器的详细信息"""
        connector_data = {"is_connector": False}
        
        try:
            if hasattr(shape, 'connector_format'):
                connector_data["is_connector"] = True
                connector_format = shape.connector_format
                
                connector_data["begin_connected"] = getattr(connector_format, 'begin_connected', False)
                connector_data["end_connected"] = getattr(connector_format, 'end_connected', False)
                
                if hasattr(connector_format, 'begin_connected_shape'):
                    connector_data["begin_shape_id"] = getattr(connector_format.begin_connected_shape, 'shape_id', None)
                
                if hasattr(connector_format, 'end_connected_shape'):
                    connector_data["end_shape_id"] = getattr(connector_format.end_connected_shape, 'shape_id', None)
                    
        except Exception as e:
            connector_data["extraction_error"] = str(e)
        
        return connector_data

    def _extract_xml_advanced(self, pptx_file: str) -> Dict[str, Any]:
        """从XML中提取高级视觉信息"""
        xml_data = {"slides": []}

        try:
            with zipfile.ZipFile(pptx_file, 'r') as zip_file:
                # 提取幻灯片XML
                slide_files = [f for f in zip_file.namelist() if f.startswith('ppt/slides/slide') and f.endswith('.xml')]

                for slide_file in sorted(slide_files):
                    slide_xml = zip_file.read(slide_file).decode('utf-8')
                    slide_data = self._parse_slide_xml_advanced(slide_xml)
                    xml_data["slides"].append(slide_data)

        except Exception as e:
            xml_data["extraction_error"] = str(e)

        return xml_data

    def _parse_slide_xml_advanced(self, slide_xml: str) -> Dict[str, Any]:
        """解析幻灯片XML的高级视觉信息"""
        slide_data = {
            "gradients": [],
            "charts": [],
            "complex_shapes": [],
            "effects": []
        }

        try:
            root = ET.fromstring(slide_xml)

            # 查找渐变定义
            for grad_fill in root.iter():
                if 'gradFill' in grad_fill.tag:
                    gradient_info = self._parse_gradient_xml(grad_fill)
                    slide_data["gradients"].append(gradient_info)

            # 查找图表
            for chart_elem in root.iter():
                if 'chart' in chart_elem.tag:
                    chart_info = self._parse_chart_xml(chart_elem)
                    slide_data["charts"].append(chart_info)

            # 查找复杂形状
            for shape_elem in root.iter():
                if 'sp' in shape_elem.tag or 'grpSp' in shape_elem.tag:
                    shape_info = self._parse_complex_shape_xml(shape_elem)
                    slide_data["complex_shapes"].append(shape_info)

        except Exception as e:
            slide_data["parsing_error"] = str(e)

        return slide_data

    def _parse_gradient_xml(self, grad_elem) -> Dict[str, Any]:
        """解析渐变XML"""
        gradient_data = {"type": "gradient"}

        try:
            # 渐变停止点
            gradient_data["stops"] = []
            for gs in grad_elem.iter():
                if 'gs' in gs.tag:
                    pos = gs.get('pos', '0')
                    gradient_data["stops"].append({"position": pos})

            # 线性渐变
            for lin in grad_elem.iter():
                if 'lin' in lin.tag:
                    gradient_data["linear"] = {
                        "angle": lin.get('ang', '0'),
                        "scaled": lin.get('scaled', 'false')
                    }

            # 路径渐变
            for path in grad_elem.iter():
                if 'path' in path.tag:
                    gradient_data["path"] = {
                        "path_type": path.get('path', 'shape'),
                        "fill_to_rect": path.get('fillToRect', '')
                    }

        except Exception as e:
            gradient_data["parsing_error"] = str(e)

        return gradient_data

    def _parse_chart_xml(self, chart_elem) -> Dict[str, Any]:
        """解析图表XML"""
        chart_data = {"type": "chart"}

        try:
            # 图表类型和数据
            chart_data["chart_reference"] = chart_elem.get('r:id', '')

        except Exception as e:
            chart_data["parsing_error"] = str(e)

        return chart_data

    def _parse_complex_shape_xml(self, shape_elem) -> Dict[str, Any]:
        """解析复杂形状XML"""
        shape_data = {"type": "complex_shape"}

        try:
            # 形状属性
            for spPr in shape_elem.iter():
                if 'spPr' in spPr.tag:
                    # 几何信息
                    for geom in spPr.iter():
                        if 'prstGeom' in geom.tag:
                            shape_data["preset_geometry"] = geom.get('prst', '')
                        elif 'custGeom' in geom.tag:
                            shape_data["custom_geometry"] = "custom"

                    # 填充信息
                    for fill in spPr.iter():
                        if 'solidFill' in fill.tag:
                            shape_data["fill_type"] = "solid"
                        elif 'gradFill' in fill.tag:
                            shape_data["fill_type"] = "gradient"
                        elif 'pattFill' in fill.tag:
                            shape_data["fill_type"] = "pattern"

                    # 效果信息
                    for effect in spPr.iter():
                        if 'effectLst' in effect.tag:
                            shape_data["has_effects"] = True

        except Exception as e:
            shape_data["parsing_error"] = str(e)

        return shape_data

    def _extract_charts_detailed(self, pptx_file: str) -> Dict[str, Any]:
        """详细提取图表数据"""
        chart_data = {"charts": []}

        try:
            prs = Presentation(pptx_file)

            for slide_idx, slide in enumerate(prs.slides):
                for shape in slide.shapes:
                    if shape.shape_type == MSO_SHAPE_TYPE.CHART:
                        detailed_chart = self._extract_single_chart_detailed(shape, slide_idx)
                        chart_data["charts"].append(detailed_chart)

        except Exception as e:
            chart_data["extraction_error"] = str(e)

        return chart_data

    def _extract_single_chart_detailed(self, shape, slide_idx: int) -> Dict[str, Any]:
        """详细提取单个图表"""
        chart_detail = {
            "slide_index": slide_idx,
            "chart_type": str(shape.chart.chart_type),
            "position": {
                "left": shape.left,
                "top": shape.top,
                "width": shape.width,
                "height": shape.height
            }
        }

        try:
            chart = shape.chart

            # 图表数据
            if hasattr(chart, 'plots') and chart.plots:
                plot = chart.plots[0]
                chart_detail["data"] = {
                    "categories": list(plot.categories) if hasattr(plot, 'categories') else [],
                    "series": []
                }

                for series in plot.series:
                    series_data = {
                        "name": series.name,
                        "values": list(series.values),
                        "format": self._extract_series_format(series)
                    }
                    chart_detail["data"]["series"].append(series_data)

            # 图表格式
            chart_detail["formatting"] = self._extract_chart_formatting(chart)

        except Exception as e:
            chart_detail["extraction_error"] = str(e)

        return chart_detail

    def _extract_series_format(self, series) -> Dict[str, Any]:
        """提取数据系列格式"""
        format_data = {}

        try:
            if hasattr(series, 'format'):
                format_obj = series.format
                format_data["fill"] = self._extract_advanced_fill(format_obj)
                format_data["line"] = self._extract_advanced_line(format_obj)

        except Exception as e:
            format_data["extraction_error"] = str(e)

        return format_data

    def _extract_chart_formatting(self, chart) -> Dict[str, Any]:
        """提取图表格式"""
        formatting = {}

        try:
            # 图表区域格式
            if hasattr(chart, 'chart_area'):
                formatting["chart_area"] = {
                    "fill": self._extract_advanced_fill(chart.chart_area),
                    "line": self._extract_advanced_line(chart.chart_area)
                }

            # 绘图区域格式
            if hasattr(chart, 'plot_area'):
                formatting["plot_area"] = {
                    "fill": self._extract_advanced_fill(chart.plot_area),
                    "line": self._extract_advanced_line(chart.plot_area)
                }

        except Exception as e:
            formatting["extraction_error"] = str(e)

        return formatting

    def _extract_gradients_detailed(self, pptx_file: str) -> Dict[str, Any]:
        """详细提取渐变信息"""
        gradient_data = {"gradients": []}

        try:
            prs = Presentation(pptx_file)

            for slide_idx, slide in enumerate(prs.slides):
                # 幻灯片背景渐变
                bg_gradient = self._extract_slide_background_gradient(slide)
                if bg_gradient:
                    gradient_data["gradients"].append({
                        "slide_index": slide_idx,
                        "type": "slide_background",
                        "gradient": bg_gradient
                    })

                # 形状渐变
                for shape_idx, shape in enumerate(slide.shapes):
                    shape_gradient = self._extract_shape_gradient(shape)
                    if shape_gradient:
                        gradient_data["gradients"].append({
                            "slide_index": slide_idx,
                            "shape_index": shape_idx,
                            "type": "shape_fill",
                            "gradient": shape_gradient
                        })

        except Exception as e:
            gradient_data["extraction_error"] = str(e)

        return gradient_data

    def _extract_slide_background_gradient(self, slide) -> Optional[Dict[str, Any]]:
        """提取幻灯片背景渐变"""
        try:
            if hasattr(slide, 'background') and hasattr(slide.background, 'fill'):
                fill = slide.background.fill
                if hasattr(fill, 'gradient') and fill.gradient:
                    return self._extract_gradient_detailed(fill.gradient)
        except:
            pass
        return None

    def _extract_shape_gradient(self, shape) -> Optional[Dict[str, Any]]:
        """提取形状渐变"""
        try:
            if hasattr(shape, 'fill') and hasattr(shape.fill, 'gradient') and shape.fill.gradient:
                return self._extract_gradient_detailed(shape.fill.gradient)
        except:
            pass
        return None

    def _extract_slide_background_advanced(self, slide) -> Dict[str, Any]:
        """提取幻灯片背景的高级信息"""
        bg_data = {"type": "none"}

        try:
            if hasattr(slide, 'background'):
                background = slide.background
                bg_data["fill"] = self._extract_advanced_fill(background)

        except Exception as e:
            bg_data["extraction_error"] = str(e)

        return bg_data

    def _extract_metadata(self, prs, pptx_file: str) -> Dict[str, Any]:
        """提取元数据"""
        return {
            "filename": os.path.basename(pptx_file),
            "slide_count": len(prs.slides),
            "slide_width": prs.slide_width,
            "slide_height": prs.slide_height,
            "slide_width_inches": round(prs.slide_width / 914400, 2),
            "slide_height_inches": round(prs.slide_height / 914400, 2)
        }


def main():
    """主函数"""
    extractor = AdvancedVisualExtractor()

    # 提取原始PPT的高级视觉元素
    original_file = "（黑红）职场工作汇报ppt-1.pptx"

    if os.path.exists(original_file):
        print(f"🎨 开始高级视觉元素提取...")
        data = extractor.extract_advanced_visuals(original_file)

        # 统计信息
        total_gradients = len(data.get("gradient_detailed_data", {}).get("gradients", []))
        total_charts = len(data.get("chart_detailed_data", {}).get("charts", []))

        print(f"📊 提取统计:")
        print(f"   渐变元素: {total_gradients} 个")
        print(f"   图表元素: {total_charts} 个")
        print(f"   幻灯片数: {data['metadata']['slide_count']} 张")

        print(f"✅ 高级视觉元素提取完成!")
    else:
        print(f"❌ 文件不存在: {original_file}")


if __name__ == "__main__":
    main()
