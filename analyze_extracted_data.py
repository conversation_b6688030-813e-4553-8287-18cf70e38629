#!/usr/bin/env python3
"""
Analysis script for extracted PPTX data.
Demonstrates how to work with the extracted content.
"""

import json
import os
from collections import Counter, defaultdict
from pathlib import Path

def load_extracted_data(json_file):
    """Load extracted data from JSON file."""
    with open(json_file, 'r', encoding='utf-8') as f:
        return json.load(f)

def analyze_text_content(data):
    """Analyze text content in the presentation."""
    print("📝 TEXT CONTENT ANALYSIS")
    print("=" * 50)
    
    all_text = []
    font_usage = Counter()
    font_sizes = []
    
    for slide_idx, slide in enumerate(data.get('slides', [])):
        slide_text = []
        
        for shape in slide.get('shapes', []):
            if 'text_content' in shape and 'error' not in shape['text_content']:
                text_content = shape['text_content']
                full_text = text_content.get('full_text', '').strip()
                
                if full_text:
                    slide_text.append(full_text)
                    all_text.append(full_text)
                
                # Analyze font usage
                for paragraph in text_content.get('paragraphs', []):
                    for run in paragraph.get('runs', []):
                        font_info = run.get('font', {})
                        font_name = font_info.get('name', 'Unknown')
                        font_size = font_info.get('size_pt')
                        
                        font_usage[font_name] += 1
                        if font_size:
                            font_sizes.append(font_size)
        
        if slide_text:
            print(f"\nSlide {slide_idx + 1}: {len(slide_text)} text elements")
            for i, text in enumerate(slide_text[:3]):  # Show first 3 texts
                preview = text[:60] + "..." if len(text) > 60 else text
                print(f"  {i+1}. {preview}")
            if len(slide_text) > 3:
                print(f"  ... and {len(slide_text) - 3} more")
    
    print(f"\n📊 STATISTICS:")
    print(f"Total text elements: {len(all_text)}")
    print(f"Total characters: {sum(len(text) for text in all_text)}")
    
    print(f"\n🔤 FONT USAGE:")
    for font, count in font_usage.most_common(5):
        print(f"  {font}: {count} uses")
    
    if font_sizes:
        print(f"\n📏 FONT SIZES:")
        print(f"  Range: {min(font_sizes):.1f}pt - {max(font_sizes):.1f}pt")
        print(f"  Average: {sum(font_sizes)/len(font_sizes):.1f}pt")

def analyze_slide_structure(data):
    """Analyze slide structure and layouts."""
    print("\n\n🏗️ SLIDE STRUCTURE ANALYSIS")
    print("=" * 50)
    
    slides = data.get('slides', [])
    layout_usage = Counter()
    shape_types = Counter()
    
    for slide in slides:
        layout_name = slide.get('layout_name', 'Unknown')
        layout_usage[layout_name] += 1
        
        for shape in slide.get('shapes', []):
            shape_type = shape.get('shape_type', 'Unknown')
            shape_types[shape_type] += 1
    
    print(f"📋 LAYOUT USAGE:")
    for layout, count in layout_usage.most_common():
        print(f"  {layout}: {count} slides")
    
    print(f"\n🔷 SHAPE TYPES:")
    for shape_type, count in shape_types.most_common(10):
        print(f"  {shape_type}: {count} shapes")

def analyze_visual_elements(data):
    """Analyze visual elements like images and positioning."""
    print("\n\n🖼️ VISUAL ELEMENTS ANALYSIS")
    print("=" * 50)
    
    images = 0
    tables = 0
    charts = 0
    groups = 0
    
    positions = []
    sizes = []
    
    for slide in data.get('slides', []):
        for shape in slide.get('shapes', []):
            # Count visual elements
            if 'image_content' in shape:
                images += 1
            if 'table_content' in shape:
                tables += 1
            if 'chart_content' in shape:
                charts += 1
            if 'group_content' in shape:
                groups += 1
            
            # Analyze positioning
            position = shape.get('position', {})
            if position:
                left = position.get('left_inches', 0)
                top = position.get('top_inches', 0)
                width = position.get('width_inches', 0)
                height = position.get('height_inches', 0)
                
                if left and top:
                    positions.append((left, top))
                if width and height:
                    sizes.append((width, height))
    
    print(f"🖼️ Images: {images}")
    print(f"📊 Tables: {tables}")
    print(f"📈 Charts: {charts}")
    print(f"👥 Groups: {groups}")
    
    if positions:
        avg_left = sum(pos[0] for pos in positions) / len(positions)
        avg_top = sum(pos[1] for pos in positions) / len(positions)
        print(f"\n📍 AVERAGE POSITION: ({avg_left:.2f}\", {avg_top:.2f}\")")
    
    if sizes:
        avg_width = sum(size[0] for size in sizes) / len(sizes)
        avg_height = sum(size[1] for size in sizes) / len(sizes)
        print(f"📏 AVERAGE SIZE: {avg_width:.2f}\" × {avg_height:.2f}\"")

def extract_all_text(data):
    """Extract all text content for further processing."""
    print("\n\n📄 EXTRACTING ALL TEXT CONTENT")
    print("=" * 50)
    
    output_file = "extracted_text_content.txt"
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("EXTRACTED TEXT CONTENT FROM PRESENTATION\n")
        f.write("=" * 50 + "\n\n")
        
        for slide_idx, slide in enumerate(data.get('slides', [])):
            f.write(f"SLIDE {slide_idx + 1}\n")
            f.write("-" * 20 + "\n")
            
            slide_texts = []
            for shape in slide.get('shapes', []):
                if 'text_content' in shape and 'error' not in shape['text_content']:
                    text_content = shape['text_content']
                    full_text = text_content.get('full_text', '').strip()
                    if full_text:
                        slide_texts.append(full_text)
            
            if slide_texts:
                for text in slide_texts:
                    f.write(f"{text}\n")
            else:
                f.write("(No text content)\n")
            
            f.write("\n")
    
    print(f"✅ All text content saved to: {output_file}")

def main():
    """Main analysis function."""
    print("PPTX EXTRACTED DATA ANALYZER")
    print("=" * 60)
    
    # Find extracted JSON files
    extracted_dir = Path("extracted_pptx_data")
    if not extracted_dir.exists():
        print("❌ No extracted_pptx_data directory found!")
        print("Please run pptx_extractor.py first.")
        return
    
    json_files = list(extracted_dir.glob("*_extracted.json"))
    if not json_files:
        print("❌ No extracted JSON files found!")
        return

    # Find the individual file (not the combined one)
    individual_files = [f for f in json_files if not f.name.startswith("all_")]
    if individual_files:
        json_file = individual_files[0]
    else:
        json_file = json_files[0]
    print(f"📁 Analyzing: {json_file.name}")
    
    try:
        data = load_extracted_data(json_file)
        
        # Print basic info
        metadata = data.get('metadata', {})
        print(f"\n📋 PRESENTATION INFO:")
        print(f"  Title: {metadata.get('title', 'Unknown')}")
        print(f"  Author: {metadata.get('author', 'Unknown')}")
        print(f"  Slides: {metadata.get('slide_count', 0)}")
        print(f"  Size: {metadata.get('slide_width_inches', 0):.1f}\" × {metadata.get('slide_height_inches', 0):.1f}\"")
        
        # Run analyses
        analyze_text_content(data)
        analyze_slide_structure(data)
        analyze_visual_elements(data)
        extract_all_text(data)
        
        print(f"\n✅ Analysis complete!")
        
    except Exception as e:
        print(f"❌ Error analyzing data: {e}")

if __name__ == "__main__":
    main()
