#!/usr/bin/env python3
"""
增强版图像提取器
专门解决PPT图像提取和复刻问题

Author: AI Assistant
Date: 2025-07-14
"""

import os
import json
import base64
import zipfile
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime

from pptx import Presentation
from pptx.enum.shapes import MSO_SHAPE_TYPE
from pptx.util import Inches, Pt
from pptx.dml.color import RGBColor

class ImageEnhancedExtractor:
    """增强版图像提取器"""
    
    def __init__(self, output_dir: str = "enhanced_extracted_data"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.images_dir = self.output_dir / "images"
        self.images_dir.mkdir(exist_ok=True)
        
    def extract_with_images(self, pptx_file: str):
        """提取PPT并保存所有图像"""
        print(f"🖼️ 开始增强版图像提取: {pptx_file}")
        
        # 首先直接从PPTX文件中提取图像
        self._extract_images_from_zip(pptx_file)
        
        # 然后分析PPT结构
        prs = Presentation(pptx_file)
        
        extraction_data = {
            "metadata": self._extract_metadata(prs, pptx_file),
            "images_info": self._analyze_images_in_ppt(prs),
            "slides": [],
            "extraction_timestamp": datetime.now().isoformat()
        }
        
        # 分析每张幻灯片
        for slide_idx, slide in enumerate(prs.slides):
            slide_data = self._extract_slide_with_images(slide, slide_idx)
            extraction_data["slides"].append(slide_data)
        
        # 保存提取数据
        output_file = self.output_dir / f"{Path(pptx_file).stem}_with_images.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(extraction_data, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"✅ 增强版提取完成: {output_file}")
        print(f"📁 图像保存目录: {self.images_dir}")
        
        return extraction_data
    
    def _extract_images_from_zip(self, pptx_file: str):
        """直接从PPTX ZIP文件中提取图像"""
        print("📦 从PPTX文件中提取图像...")
        
        image_count = 0
        
        try:
            with zipfile.ZipFile(pptx_file, 'r') as zip_file:
                # 查找所有图像文件
                for file_info in zip_file.filelist:
                    if file_info.filename.startswith('ppt/media/'):
                        # 提取图像文件
                        image_data = zip_file.read(file_info.filename)
                        
                        # 确定文件扩展名
                        original_name = file_info.filename.split('/')[-1]
                        
                        # 保存图像文件
                        image_path = self.images_dir / original_name
                        with open(image_path, 'wb') as img_file:
                            img_file.write(image_data)
                        
                        image_count += 1
                        print(f"  💾 保存图像: {original_name}")
        
        except Exception as e:
            print(f"⚠️ 图像提取警告: {e}")
        
        print(f"📊 总计提取 {image_count} 个图像文件")
        return image_count
    
    def _analyze_images_in_ppt(self, prs: Presentation) -> Dict[str, Any]:
        """分析PPT中的图像信息"""
        images_info = {
            "total_images": 0,
            "image_shapes": [],
            "image_files": []
        }
        
        # 统计所有图像形状
        for slide_idx, slide in enumerate(prs.slides):
            for shape_idx, shape in enumerate(slide.shapes):
                if shape.shape_type == MSO_SHAPE_TYPE.PICTURE:
                    images_info["total_images"] += 1
                    
                    image_info = {
                        "slide_index": slide_idx,
                        "shape_index": shape_idx,
                        "shape_id": getattr(shape, 'shape_id', None),
                        "position": {
                            "left": shape.left,
                            "top": shape.top,
                            "width": shape.width,
                            "height": shape.height,
                        }
                    }
                    
                    images_info["image_shapes"].append(image_info)
        
        # 列出提取的图像文件
        if self.images_dir.exists():
            for img_file in self.images_dir.glob("*"):
                if img_file.is_file():
                    images_info["image_files"].append({
                        "filename": img_file.name,
                        "size": img_file.stat().st_size,
                        "path": str(img_file)
                    })
        
        return images_info
    
    def _extract_metadata(self, prs: Presentation, file_path: str) -> Dict[str, Any]:
        """提取基本元数据"""
        core_props = prs.core_properties
        
        return {
            "file_path": file_path,
            "file_size": os.path.getsize(file_path),
            "slide_count": len(prs.slides),
            "slide_width": prs.slide_width,
            "slide_height": prs.slide_height,
            "slide_width_inches": prs.slide_width / 914400,
            "slide_height_inches": prs.slide_height / 914400,
            "title": getattr(core_props, 'title', None),
            "author": getattr(core_props, 'author', None),
        }
    
    def _extract_slide_with_images(self, slide, slide_idx: int) -> Dict[str, Any]:
        """提取幻灯片信息，特别关注图像"""
        slide_data = {
            "slide_index": slide_idx,
            "shapes": [],
            "image_count": 0
        }
        
        for shape_idx, shape in enumerate(slide.shapes):
            shape_data = {
                "shape_index": shape_idx,
                "shape_type": str(shape.shape_type),
                "position": {
                    "left": shape.left,
                    "top": shape.top,
                    "width": shape.width,
                    "height": shape.height,
                }
            }
            
            # 特别处理图像
            if shape.shape_type == MSO_SHAPE_TYPE.PICTURE:
                slide_data["image_count"] += 1
                shape_data["is_image"] = True
                shape_data["image_info"] = {
                    "estimated_filename": f"image{slide_data['image_count']}.png",
                    "shape_id": getattr(shape, 'shape_id', None),
                }
            
            # 处理文本
            elif hasattr(shape, 'text') and shape.text.strip():
                shape_data["text"] = shape.text.strip()
            
            slide_data["shapes"].append(shape_data)
        
        return slide_data


class ImageEnhancedRecreator:
    """增强版图像复刻器"""
    
    def __init__(self, images_dir: str = "enhanced_extracted_data/images"):
        self.images_dir = Path(images_dir)
        self.maotai_replacements = self._create_maotai_replacements()
    
    def _create_maotai_replacements(self):
        """创建茅台替换规则"""
        return {
            "职场可视化逻辑图ppt模板": "茅台集团数字化转型战略汇报",
            "某某某": "丁雄军",
            "电商平台": "茅台电商平台",
            "100万": "500万箱",
            "86%": "95.2%",
            "轻健康": "高端白酒消费",
            "LOGO": "茅台LOGO",
        }
    
    def recreate_with_images(self, json_file: str, output_file: str = "茅台集团PPT_含图像版.pptx"):
        """复刻PPT并尝试包含图像"""
        print(f"🏗️ 开始图像增强版复刻: {json_file}")
        
        # 加载数据
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 创建演示文稿
        prs = Presentation()
        
        # 设置尺寸
        metadata = data.get('metadata', {})
        if 'slide_width' in metadata:
            prs.slide_width = metadata['slide_width']
            prs.slide_height = metadata['slide_height']
        
        # 获取可用图像
        available_images = self._get_available_images()
        image_index = 0
        
        # 复刻幻灯片
        slides_data = data.get('slides', [])
        
        for slide_idx, slide_data in enumerate(slides_data):
            if slide_idx % 20 == 0:
                print(f"🔄 处理幻灯片 {slide_idx + 1}/{len(slides_data)}")
            
            # 创建幻灯片
            slide_layout = prs.slide_layouts[6]  # 空白布局
            slide = prs.slides.add_slide(slide_layout)
            
            # 添加形状
            for shape_data in slide_data.get('shapes', []):
                try:
                    if shape_data.get('is_image', False):
                        # 处理图像
                        self._add_image_shape(slide, shape_data, available_images, image_index)
                        image_index += 1
                    elif 'text' in shape_data:
                        # 处理文本
                        self._add_text_shape(slide, shape_data)
                    else:
                        # 其他形状
                        self._add_placeholder_shape(slide, shape_data)
                except Exception as e:
                    continue
        
        # 设置属性
        core_props = prs.core_properties
        core_props.title = "茅台集团数字化转型战略汇报"
        core_props.author = "茅台集团"
        
        # 保存
        prs.save(output_file)
        print(f"✅ 图像增强版复刻完成: {output_file}")
        print(f"📊 使用了 {min(image_index, len(available_images))} 个图像")
        
        return output_file
    
    def _get_available_images(self) -> List[Path]:
        """获取可用的图像文件"""
        if not self.images_dir.exists():
            return []
        
        image_extensions = {'.png', '.jpg', '.jpeg', '.gif', '.bmp', '.tiff'}
        images = []
        
        for img_file in self.images_dir.iterdir():
            if img_file.is_file() and img_file.suffix.lower() in image_extensions:
                images.append(img_file)
        
        return sorted(images)
    
    def _add_image_shape(self, slide, shape_data: Dict[str, Any], available_images: List[Path], image_index: int):
        """添加图像形状"""
        position = shape_data.get('position', {})
        left = position.get('left', 0)
        top = position.get('top', 0)
        width = position.get('width', 914400)
        height = position.get('height', 914400)
        
        try:
            if image_index < len(available_images):
                # 使用实际图像
                image_path = available_images[image_index]
                slide.shapes.add_picture(str(image_path), left, top, width, height)
            else:
                # 创建茅台LOGO占位符
                shape = slide.shapes.add_shape(1, left, top, width, height)
                fill = shape.fill
                fill.solid()
                fill.fore_color.rgb = RGBColor(200, 16, 46)  # 茅台红
                
                if hasattr(shape, 'text_frame'):
                    shape.text_frame.text = "茅台LOGO"
                    shape.text_frame.paragraphs[0].font.color.rgb = RGBColor(255, 255, 255)
                    shape.text_frame.paragraphs[0].font.size = Pt(12)
        except Exception as e:
            # 如果图像添加失败，创建占位符
            self._add_placeholder_shape(slide, shape_data)
    
    def _add_text_shape(self, slide, shape_data: Dict[str, Any]):
        """添加文本形状"""
        position = shape_data.get('position', {})
        left = position.get('left', 0)
        top = position.get('top', 0)
        width = position.get('width', 914400)
        height = position.get('height', 914400)
        
        text = shape_data.get('text', '')
        
        # 应用茅台替换
        for original, replacement in self.maotai_replacements.items():
            text = text.replace(original, replacement)
        
        try:
            textbox = slide.shapes.add_textbox(left, top, width, height)
            text_frame = textbox.text_frame
            text_frame.text = text
            text_frame.word_wrap = True
            
            # 设置茅台风格
            if text_frame.paragraphs:
                p = text_frame.paragraphs[0]
                p.font.name = '微软雅黑'
                p.font.color.rgb = RGBColor(200, 16, 46)
                p.font.size = Pt(12)
        except:
            pass
    
    def _add_placeholder_shape(self, slide, shape_data: Dict[str, Any]):
        """添加占位符形状"""
        position = shape_data.get('position', {})
        left = position.get('left', 0)
        top = position.get('top', 0)
        width = position.get('width', 914400)
        height = position.get('height', 914400)
        
        try:
            shape = slide.shapes.add_shape(1, left, top, width, height)
            fill = shape.fill
            fill.solid()
            fill.fore_color.rgb = RGBColor(255, 215, 0)  # 金色
            fill.transparency = 0.8
        except:
            pass


def main():
    """主函数"""
    print("🖼️ 图像增强版PPT提取和复刻器")
    print("=" * 60)
    
    # 原始PPT文件
    pptx_file = "（黑红）职场工作汇报ppt-1.pptx"
    
    if not os.path.exists(pptx_file):
        print(f"❌ 未找到文件: {pptx_file}")
        return
    
    # 步骤1: 增强版提取
    extractor = ImageEnhancedExtractor()
    extraction_data = extractor.extract_with_images(pptx_file)
    
    # 步骤2: 图像增强版复刻
    recreator = ImageEnhancedRecreator()
    json_file = "enhanced_extracted_data/（黑红）职场工作汇报ppt-1_with_images.json"
    
    if os.path.exists(json_file):
        output_file = recreator.recreate_with_images(json_file)
        
        print(f"\n🎉 图像增强版处理完成!")
        print(f"📄 输出文件: {output_file}")
        print(f"📁 图像目录: enhanced_extracted_data/images/")
        
        # 生成报告
        generate_image_report(extraction_data, output_file)
    else:
        print(f"❌ 未找到提取数据文件: {json_file}")


def generate_image_report(extraction_data: Dict[str, Any], output_file: str):
    """生成图像处理报告"""
    report_file = "图像处理报告.md"
    
    images_info = extraction_data.get('images_info', {})
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(f"""# 图像处理报告

## 📊 图像统计
- **PPT中图像形状数量**: {images_info.get('total_images', 0)}
- **提取的图像文件数量**: {len(images_info.get('image_files', []))}
- **输出PPT文件**: {output_file}

## 🖼️ 提取的图像文件
""")
        
        for img_info in images_info.get('image_files', []):
            f.write(f"- {img_info['filename']} ({img_info['size']} bytes)\n")
        
        f.write(f"""
## 🔧 处理说明
1. **图像提取**: 直接从PPTX ZIP文件中提取所有媒体文件
2. **图像复刻**: 在复刻的PPT中使用提取的图像
3. **占位符**: 对于无法处理的图像，使用茅台红色占位符

## 💡 使用建议
1. 检查 `enhanced_extracted_data/images/` 目录中的图像文件
2. 可以手动替换为茅台相关的产品图片
3. 重新运行复刻器以更新PPT

---
*图像处理报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
""")
    
    print(f"📋 图像处理报告已生成: {report_file}")


if __name__ == "__main__":
    main()
