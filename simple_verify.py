#!/usr/bin/env python3
"""
简化的茅台PPT验证脚本
"""

from pptx import Presentation
import os

def simple_verify():
    """简单验证PPT"""
    maotai_file = "茅台集团数字化转型战略汇报_最终版.pptx"
    
    print(f"🔍 验证文件: {maotai_file}")
    
    if not os.path.exists(maotai_file):
        print("❌ 文件不存在")
        return
    
    try:
        prs = Presentation(maotai_file)
        
        print(f"✅ 文件加载成功")
        print(f"📊 幻灯片数量: {len(prs.slides)}")
        print(f"📏 尺寸: {prs.slide_width/914400:.1f}\" × {prs.slide_height/914400:.1f}\"")
        print(f"💾 文件大小: {os.path.getsize(maotai_file)/1024/1024:.2f} MB")
        
        # 检查属性
        core_props = prs.core_properties
        print(f"📋 标题: {core_props.title}")
        print(f"👤 作者: {core_props.author}")
        
        # 检查第一张幻灯片
        if prs.slides:
            first_slide = prs.slides[0]
            print(f"🔍 第一张幻灯片有 {len(first_slide.shapes)} 个形状")
            
            text_count = 0
            for shape in first_slide.shapes:
                try:
                    if hasattr(shape, 'text') and shape.text.strip():
                        text_count += 1
                        if text_count <= 3:  # 只显示前3个
                            print(f"   文本: {shape.text.strip()[:50]}...")
                except:
                    continue
            
            print(f"   总计 {text_count} 个文本元素")
        
        print(f"✅ 验证完成 - PPT生成成功!")
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")

if __name__ == "__main__":
    simple_verify()
