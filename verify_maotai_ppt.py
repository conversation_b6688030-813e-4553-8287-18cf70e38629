#!/usr/bin/env python3
"""
茅台PPT验证脚本
验证生成的茅台PPT内容和结构

Author: AI Assistant
Date: 2025-07-14
"""

from pptx import Presentation
import os
from pathlib import Path

def verify_maotai_ppt(pptx_file: str):
    """验证茅台PPT内容"""
    print(f"🔍 验证PPT文件: {pptx_file}")
    
    if not os.path.exists(pptx_file):
        print(f"❌ 文件不存在: {pptx_file}")
        return False
    
    try:
        prs = Presentation(pptx_file)
        
        print(f"✅ PPT文件加载成功")
        print(f"📊 基本信息:")
        print(f"   - 幻灯片数量: {len(prs.slides)}")
        print(f"   - 幻灯片尺寸: {prs.slide_width/914400:.1f}\" × {prs.slide_height/914400:.1f}\"")
        print(f"   - 文件大小: {os.path.getsize(pptx_file)/1024/1024:.2f} MB")
        
        # 检查演示文稿属性
        core_props = prs.core_properties
        print(f"\n📋 演示文稿属性:")
        print(f"   - 标题: {core_props.title}")
        print(f"   - 作者: {core_props.author}")
        print(f"   - 主题: {core_props.subject}")
        print(f"   - 关键词: {core_props.keywords}")
        
        # 分析前几张幻灯片的内容
        print(f"\n📄 内容分析:")
        maotai_keywords = ["茅台", "白酒", "数字化", "转型", "战略", "集团"]
        total_maotai_mentions = 0
        
        for slide_idx, slide in enumerate(prs.slides[:10]):  # 检查前10张幻灯片
            slide_text = []
            shape_count = len(slide.shapes)
            
            for shape in slide.shapes:
                if hasattr(shape, 'text') and shape.text.strip():
                    slide_text.append(shape.text.strip())
            
            # 统计茅台相关关键词
            slide_content = " ".join(slide_text)
            slide_maotai_count = sum(slide_content.count(keyword) for keyword in maotai_keywords)
            total_maotai_mentions += slide_maotai_count
            
            print(f"   幻灯片 {slide_idx + 1}: {shape_count} 个形状, {len(slide_text)} 个文本元素")
            if slide_text:
                # 显示前3个文本内容
                for i, text in enumerate(slide_text[:3]):
                    preview = text[:40] + "..." if len(text) > 40 else text
                    print(f"     - {preview}")
                if len(slide_text) > 3:
                    print(f"     - ... 还有 {len(slide_text) - 3} 个文本元素")
        
        print(f"\n🎯 茅台品牌相关性:")
        print(f"   - 茅台相关关键词总计: {total_maotai_mentions} 次")
        print(f"   - 品牌适配度: {'高' if total_maotai_mentions > 20 else '中' if total_maotai_mentions > 10 else '低'}")
        
        # 检查是否有错误或异常
        print(f"\n✅ 验证完成 - PPT结构完整，内容已成功替换为茅台相关内容")
        return True
        
    except Exception as e:
        print(f"❌ 验证过程中出现错误: {e}")
        return False

def compare_with_original():
    """与原始PPT进行对比"""
    original_file = "（黑红）职场工作汇报ppt-1.pptx"
    maotai_file = "茅台集团数字化转型战略汇报.pptx"
    
    print(f"\n📊 对比分析:")
    
    if os.path.exists(original_file) and os.path.exists(maotai_file):
        original_size = os.path.getsize(original_file) / 1024 / 1024
        maotai_size = os.path.getsize(maotai_file) / 1024 / 1024
        
        print(f"   原始文件大小: {original_size:.2f} MB")
        print(f"   茅台文件大小: {maotai_size:.2f} MB")
        print(f"   大小比例: {(maotai_size/original_size)*100:.1f}%")
        
        # 检查幻灯片数量
        try:
            original_prs = Presentation(original_file)
            maotai_prs = Presentation(maotai_file)
            
            print(f"   原始幻灯片数: {len(original_prs.slides)}")
            print(f"   茅台幻灯片数: {len(maotai_prs.slides)}")
            print(f"   结构保持度: {'100%' if len(original_prs.slides) == len(maotai_prs.slides) else '不完整'}")
            
        except Exception as e:
            print(f"   对比分析失败: {e}")
    else:
        print(f"   无法进行对比 - 缺少文件")

def generate_content_sample():
    """生成内容样本展示"""
    maotai_file = "茅台集团数字化转型战略汇报.pptx"
    
    if not os.path.exists(maotai_file):
        print("❌ 茅台PPT文件不存在")
        return
    
    try:
        prs = Presentation(maotai_file)
        
        print(f"\n📝 内容样本展示:")
        print("=" * 50)
        
        sample_slides = [0, 3, 10, 20, 30]  # 选择几张代表性幻灯片
        
        for slide_idx in sample_slides:
            if slide_idx < len(prs.slides):
                slide = prs.slides[slide_idx]
                print(f"\n【幻灯片 {slide_idx + 1}】")
                print("-" * 30)
                
                texts = []
                for shape in slide.shapes:
                    if hasattr(shape, 'text') and shape.text.strip():
                        texts.append(shape.text.strip())
                
                if texts:
                    for i, text in enumerate(texts[:5]):  # 显示前5个文本
                        print(f"{i+1}. {text}")
                    if len(texts) > 5:
                        print(f"... 还有 {len(texts) - 5} 个文本元素")
                else:
                    print("(无文本内容)")
        
    except Exception as e:
        print(f"❌ 内容样本生成失败: {e}")

def main():
    """主函数"""
    print("🍷 茅台PPT验证工具")
    print("=" * 60)
    
    maotai_file = "茅台集团数字化转型战略汇报.pptx"
    
    # 验证PPT
    success = verify_maotai_ppt(maotai_file)
    
    if success:
        # 对比分析
        compare_with_original()
        
        # 内容样本
        generate_content_sample()
        
        print(f"\n🎉 验证完成!")
        print(f"✅ 茅台PPT已成功生成并验证")
        print(f"📁 文件位置: {os.path.abspath(maotai_file)}")
        
        # 使用建议
        print(f"\n💡 使用建议:")
        print(f"1. 可以直接用PowerPoint打开查看效果")
        print(f"2. 建议根据实际需求调整具体内容")
        print(f"3. 可以替换占位符图片为实际茅台产品图片")
        print(f"4. 根据最新数据更新相关业绩数字")
        
    else:
        print(f"❌ 验证失败，请检查PPT生成过程")

if __name__ == "__main__":
    main()
