#!/usr/bin/env python3
"""
Auto-generated script to recreate PowerPoint presentation from extracted data.
Generated by PPTXExtractor on 2025-07-14T05:24:28.137608
"""

from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.enum.text import PP_ALIGN
from pptx.dml.color import RGBColor
import json

def recreate_presentation(data_file, output_file):
    """Recreate presentation from extracted data."""
    with open(data_file, 'r', encoding='utf-8') as f:
        data = json.load(f)

    # Create new presentation
    prs = Presentation()

    # Set slide dimensions
    metadata = data.get('metadata', {})
    if 'slide_width' in metadata and 'slide_height' in metadata:
        prs.slide_width = metadata['slide_width']
        prs.slide_height = metadata['slide_height']

    # Recreate slides
    for slide_data in data.get('slides', []):
        slide_layout = prs.slide_layouts[0]  # Use blank layout
        slide = prs.slides.add_slide(slide_layout)

        # Add shapes to slide
        for shape_data in slide_data.get('shapes', []):
            recreate_shape(slide, shape_data)

    # Save presentation
    prs.save(output_file)
    print(f"Recreated presentation saved as: {output_file}")

def recreate_shape(slide, shape_data):
    """Recreate a shape on the slide."""
    # This is a simplified recreation - full implementation would be more complex
    shape_type = shape_data.get('shape_type', '')
    position = shape_data.get('position', {})

    if 'text_content' in shape_data:
        # Create text box
        left = Inches(position.get('left_inches', 0))
        top = Inches(position.get('top_inches', 0))
        width = Inches(position.get('width_inches', 1))
        height = Inches(position.get('height_inches', 1))

        textbox = slide.shapes.add_textbox(left, top, width, height)
        text_frame = textbox.text_frame
        text_frame.text = shape_data['text_content'].get('full_text', '')

if __name__ == "__main__":
    import sys
    if len(sys.argv) != 3:
        print("Usage: python recreate_presentation.py <data_file.json> <output.pptx>")
        sys.exit(1)

    recreate_presentation(sys.argv[1], sys.argv[2])
