#!/usr/bin/env python3
"""
茅台集团专用文本替换器
基于提取的文本结构，批量替换为茅台集团相关内容

Author: AI Assistant
Date: 2025-07-14
"""

import os
import json
from pathlib import Path
from pptx_text_replacer import PPTXTextReplacer

class MaotaiTextReplacer:
    """茅台集团专用文本替换器"""
    
    def __init__(self):
        self.replacer = PPTXTextReplacer()
        
        # 茅台集团文本替换映射
        self.maotai_replacements = {
            # 标题替换
            "职场可视化逻辑图ppt模板": "茅台集团数字化转型战略汇报",
            "Workplace Visualization Logic Diagram PowerPoint Template": "Moutai Group Digital Transformation Strategy Report",
            
            # 公司信息替换
            "某某某": "茅台集团",
            "汇报人": "汇报单位",
            "LOGO": "茅台LOGO",
            
            # 内容替换
            "职场可视化逻辑图ppt模板助您高效开展工作": "茅台集团致力于传统酒业的数字化转型升级",
            "此处为过渡页": "茅台集团数字化转型",
            "在此处输入大标题": "茅台集团发展战略",
            "点击输入标题": "茅台数字化转型",
            "点击输入总结内容": "茅台集团发展总结",
            "此处可输入正文内容；此处可输入正文内容；此处可输入正文内容": "茅台集团作为中国白酒行业的领军企业，积极推进数字化转型，提升品牌价值和市场竞争力。",
            "此处输入标题": "茅台品牌价值",
            "Please enter your title here": "Moutai Brand Value",
            
            # 目录和章节
            "目 录": "汇报目录",
            "CONTENTS": "CONTENTS",
            
            # 业务相关
            "工作汇报": "茅台发展汇报",
            "年度总结": "茅台年度发展总结",
            "项目进展": "茅台数字化项目进展",
            "市场分析": "茅台市场分析",
            "发展规划": "茅台发展规划",
            
            # 数据相关
            "销售数据": "茅台销售数据",
            "市场份额": "茅台市场份额",
            "增长率": "茅台增长率",
            "利润分析": "茅台利润分析",
            
            # 战略相关
            "发展战略": "茅台发展战略",
            "品牌建设": "茅台品牌建设",
            "数字化转型": "茅台数字化转型",
            "创新发展": "茅台创新发展",
            
            # 时间相关
            "2024年3月1日": "2024年12月",
            "2024": "2024",
            "2023": "2023",
            
            # 通用替换
            "企业": "茅台集团",
            "公司": "茅台",
            "品牌": "茅台品牌",
            "产品": "茅台产品"
        }
    
    def create_maotai_replacement_file(self, text_structure_file: str) -> str:
        """创建茅台专用的文本替换文件"""
        print("🍶 创建茅台集团专用文本替换文件...")
        
        # 加载文本结构
        with open(text_structure_file, 'r', encoding='utf-8') as f:
            text_structure = json.load(f)
        
        # 创建茅台替换映射
        maotai_replacement_map = {}
        
        for text_id, original_text in text_structure["text_map"].items():
            new_text = self._get_maotai_replacement(original_text)
            
            maotai_replacement_map[text_id] = {
                "original_text": original_text,
                "new_text": new_text,
                "location_info": self._get_location_info(text_id, text_structure),
                "changed": new_text != original_text
            }
        
        # 保存茅台替换文件
        output_file = "pptx_text_data/maotai_text_replacements.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(maotai_replacement_map, f, indent=2, ensure_ascii=False)
        
        # 统计替换数量
        changed_count = sum(1 for item in maotai_replacement_map.values() if item["changed"])
        
        print(f"✅ 茅台文本替换文件已创建: {output_file}")
        print(f"📊 替换统计: {changed_count}/{len(maotai_replacement_map)} 个文本将被替换")
        
        return output_file
    
    def _get_maotai_replacement(self, original_text: str) -> str:
        """获取茅台替换文本"""
        # 精确匹配
        if original_text in self.maotai_replacements:
            return self.maotai_replacements[original_text]
        
        # 部分匹配替换
        new_text = original_text
        for old_word, new_word in self.maotai_replacements.items():
            if old_word in new_text:
                new_text = new_text.replace(old_word, new_word)
        
        return new_text
    
    def _get_location_info(self, text_id: str, text_structure: dict) -> str:
        """获取文本位置信息"""
        for slide in text_structure["slides"]:
            for text_element in slide["text_elements"]:
                if text_element["text_id"] == text_id:
                    location = text_element["location"]
                    return f"幻灯片{location['slide_index']+1} - {location['shape_name']}"
        return "未知位置"
    
    def apply_maotai_replacements(self, original_pptx: str, replacement_file: str, output_pptx: str):
        """应用茅台文本替换"""
        print("🔄 开始应用茅台文本替换...")
        
        # 加载替换映射
        with open(replacement_file, 'r', encoding='utf-8') as f:
            replacements = json.load(f)
        
        # 应用替换
        replacement_count = self.replacer.apply_text_replacements(
            original_pptx, replacements, output_pptx
        )
        
        return replacement_count
    
    def create_replacement_summary(self, replacement_file: str) -> str:
        """创建替换摘要报告"""
        with open(replacement_file, 'r', encoding='utf-8') as f:
            replacements = json.load(f)
        
        # 统计信息
        total_texts = len(replacements)
        changed_texts = sum(1 for item in replacements.values() if item["changed"])
        unchanged_texts = total_texts - changed_texts
        
        # 创建摘要
        summary = {
            "replacement_summary": {
                "total_text_elements": total_texts,
                "changed_elements": changed_texts,
                "unchanged_elements": unchanged_texts,
                "change_percentage": round((changed_texts / total_texts) * 100, 2)
            },
            "sample_changes": []
        }
        
        # 添加示例更改
        change_count = 0
        for text_id, item in replacements.items():
            if item["changed"] and change_count < 20:  # 只显示前20个更改
                summary["sample_changes"].append({
                    "text_id": text_id,
                    "location": item["location_info"],
                    "original": item["original_text"],
                    "new": item["new_text"]
                })
                change_count += 1
        
        # 保存摘要
        summary_file = "pptx_text_data/maotai_replacement_summary.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)
        
        print(f"📋 替换摘要已创建: {summary_file}")
        return summary_file


def main():
    """主函数"""
    maotai_replacer = MaotaiTextReplacer()
    
    # 文件路径
    original_pptx = "（黑红）职场工作汇报ppt-1.pptx"
    text_structure_file = "pptx_text_data/（黑红）职场工作汇报ppt-1_text_structure.json"
    output_pptx = "茅台集团数字化转型战略汇报_完整版.pptx"
    
    if not os.path.exists(original_pptx):
        print(f"❌ 原始文件不存在: {original_pptx}")
        return
    
    if not os.path.exists(text_structure_file):
        print(f"❌ 文本结构文件不存在: {text_structure_file}")
        print("请先运行 pptx_text_replacer.py 生成文本结构文件")
        return
    
    print("🍶 开始茅台集团专用文本替换流程...")
    
    # 步骤1: 创建茅台替换文件
    replacement_file = maotai_replacer.create_maotai_replacement_file(text_structure_file)
    
    # 步骤2: 创建替换摘要
    summary_file = maotai_replacer.create_replacement_summary(replacement_file)
    
    # 步骤3: 应用替换
    replacement_count = maotai_replacer.apply_maotai_replacements(
        original_pptx, replacement_file, output_pptx
    )
    
    print("✅ 茅台集团文本替换完成!")
    print(f"📁 生成的文件:")
    print(f"   - 替换映射: {replacement_file}")
    print(f"   - 替换摘要: {summary_file}")
    print(f"   - 茅台版PPT: {output_pptx}")
    print(f"📊 实际替换: {replacement_count} 处文本")


if __name__ == "__main__":
    main()
