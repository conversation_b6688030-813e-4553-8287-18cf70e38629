#!/usr/bin/env python3
"""
增强版茅台PPT复刻器
改进内容替换逻辑，确保更完整的内容转换

Author: AI Assistant
Date: 2025-07-14
"""

import json
import os
import re
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional

from pptx import Presentation
from pptx.util import Inches, Pt, Cm
from pptx.enum.text import PP_ALIGN, MSO_ANCHOR, MSO_AUTO_SIZE
from pptx.enum.shapes import MSO_SHAPE_TYPE
from pptx.dml.color import RGBColor, ColorFormat
from pptx.enum.dml import MSO_THEME_COLOR
from maotai_content_database import MaotaiContentDatabase

class EnhancedMaotaiRecreator:
    """增强版茅台PPT复刻器"""
    
    def __init__(self):
        self.content_mapping = MaotaiContentDatabase.get_comprehensive_mapping()
        self.color_scheme = MaotaiContentDatabase.get_color_scheme()
        self.maotai_content = MaotaiContentDatabase.get_maotai_specific_content()
        self.business_data = MaotaiContentDatabase.get_business_data()
        
        # 添加更多智能替换规则
        self.smart_replacements = self._create_smart_replacements()
    
    def _create_smart_replacements(self):
        """创建智能替换规则"""
        return [
            # 完整短语替换
            (r'职场可视化逻辑图ppt模板', '茅台集团数字化转型战略汇报'),
            (r'Workplace Visualization Logic Diagram PowerPoint Template', 'Moutai Group Digital Transformation Strategy Report'),
            
            # 公司名称替换
            (r'某某某', '丁雄军'),
            (r'xxx公司', '茅台集团'),
            (r'XX电商平台', '茅台电商平台'),
            
            # 业务描述替换
            (r'体育、运动、户外、健康、生活等多个板块', '茅台酒、系列酒、文创产品、数字化业务等多个板块'),
            (r'服饰、生活用品、娱乐设施、美食、日常出行以及阅读学习', '茅台酒、茅台王子酒、茅台迎宾酒、茅台文创、茅台冰淇淋、茅台巧克力'),
            
            # 行业术语替换
            (r'轻健康', '高端白酒消费'),
            (r'户外运动', '高端商务消费'),
            (r'旅游度假', '商务宴请场景'),
            
            # 通用描述替换
            (r'此处可输入正文内容[；;。]*', '茅台集团作为中国白酒行业领军企业，始终坚持高质量发展战略'),
            (r'此处为过渡页', '茅台品牌价值持续提升'),
            (r'助您高效开展工作', '引领白酒行业发展新篇章'),
            
            # 数据替换
            (r'100万', '500万箱'),
            (r'86%', '95.2%'),
            (r'150%', '185.6%'),
            (r'50%', '78.9%'),
            (r'30%', '45.3%'),
            (r'120%', '168.7%'),
        ]
    
    def enhanced_replace_content(self, text: str) -> str:
        """增强的内容替换函数"""
        if not text or not text.strip():
            return text
        
        result = text
        
        # 首先应用智能替换规则
        for pattern, replacement in self.smart_replacements:
            result = re.sub(pattern, replacement, result, flags=re.IGNORECASE)
        
        # 然后应用基础映射表
        for original, replacement in self.content_mapping.items():
            if original in result:
                result = result.replace(original, replacement)
        
        # 特殊处理：如果文本很短且是标题类型，进行特殊替换
        if len(result.strip()) < 50:
            result = self._handle_short_text(result)
        
        return result
    
    def _handle_short_text(self, text: str) -> str:
        """处理短文本（通常是标题）"""
        text = text.strip()
        
        # 标题类型的特殊处理
        title_mappings = {
            '在此处输入大标题': '茅台集团战略发展规划',
            '点击输入标题': '茅台品牌价值提升',
            '输入标题': '茅台核心竞争力',
            '大标题': '茅台集团发展战略',
            '标题': '茅台品牌建设',
            'LOGO': '茅台LOGO',
            '汇报人': '汇报人',
            '目 录': '目 录',
        }
        
        for original, replacement in title_mappings.items():
            if text == original:
                return replacement
        
        return text
    
    def recreate_presentation_enhanced(self, json_file: str, output_file: str = "茅台集团数字化转型战略汇报_增强版.pptx"):
        """增强版演示文稿复刻"""
        print(f"🏗️ 开始增强版复刻: {json_file}")
        
        # 加载提取的数据
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 创建新演示文稿
        prs = Presentation()
        
        # 设置幻灯片尺寸
        metadata = data.get('metadata', {})
        if 'slide_width' in metadata and 'slide_height' in metadata:
            prs.slide_width = metadata['slide_width']
            prs.slide_height = metadata['slide_height']
        
        print(f"📏 设置幻灯片尺寸: {metadata.get('slide_width_inches', 0):.1f}\" × {metadata.get('slide_height_inches', 0):.1f}\"")
        
        # 复刻每一张幻灯片
        slides_data = data.get('slides', [])
        total_slides = len(slides_data)
        
        replacement_count = 0
        
        for slide_idx, slide_data in enumerate(slides_data):
            if slide_idx % 10 == 0:  # 每10张幻灯片显示一次进度
                print(f"🔄 处理幻灯片 {slide_idx + 1}/{total_slides}")
            
            # 创建空白幻灯片
            slide_layout = prs.slide_layouts[6]  # 使用空白布局
            slide = prs.slides.add_slide(slide_layout)
            
            # 添加形状到幻灯片
            shapes_data = slide_data.get('shapes', [])
            for shape_data in shapes_data:
                try:
                    replaced = self._recreate_shape_enhanced(slide, shape_data)
                    if replaced:
                        replacement_count += 1
                except Exception as e:
                    continue
        
        # 设置演示文稿属性
        core_props = prs.core_properties
        core_props.title = "茅台集团数字化转型战略汇报"
        core_props.author = "茅台集团"
        core_props.subject = "数字化转型战略"
        core_props.keywords = "茅台,数字化,转型,战略,白酒"
        core_props.comments = "茅台集团数字化转型战略汇报演示文稿"
        core_props.category = "企业汇报"
        
        # 保存演示文稿
        prs.save(output_file)
        print(f"✅ 增强版PPT复刻完成: {output_file}")
        print(f"📊 总计处理 {total_slides} 张幻灯片")
        print(f"🔄 内容替换次数: {replacement_count}")
        
        return output_file
    
    def _recreate_shape_enhanced(self, slide, shape_data: Dict[str, Any]) -> bool:
        """增强版形状重建"""
        shape_type = shape_data.get('shape_type', '')
        position = shape_data.get('position', {})
        
        # 获取位置信息
        left = position.get('left', 0)
        top = position.get('top', 0)
        width = position.get('width', 914400)
        height = position.get('height', 914400)
        
        replaced = False
        
        # 处理文本框和占位符
        if 'text_content' in shape_data and shape_data['text_content']:
            replaced = self._create_text_shape_enhanced(slide, shape_data, left, top, width, height)
        
        # 处理其他类型的形状
        elif 'image_content' in shape_data:
            self._create_image_placeholder(slide, left, top, width, height)
        elif 'table_content' in shape_data:
            self._create_table_shape(slide, shape_data, left, top, width, height)
        elif 'chart_content' in shape_data:
            self._create_chart_placeholder(slide, left, top, width, height)
        elif 'group_content' in shape_data:
            self._create_group_shapes(slide, shape_data)
        else:
            self._create_shape_placeholder(slide, left, top, width, height)
        
        return replaced
    
    def _create_text_shape_enhanced(self, slide, shape_data: Dict[str, Any], left: int, top: int, width: int, height: int) -> bool:
        """增强版文本形状创建"""
        text_content = shape_data.get('text_content', {})
        if 'error' in text_content:
            return False
        
        full_text = text_content.get('full_text', '')
        if not full_text.strip():
            return False
        
        # 使用增强的替换函数
        original_text = full_text
        maotai_text = self.enhanced_replace_content(full_text)
        
        # 检查是否有替换发生
        content_replaced = (original_text != maotai_text)
        
        # 创建文本框
        try:
            textbox = slide.shapes.add_textbox(left, top, width, height)
            text_frame = textbox.text_frame
            text_frame.clear()
            
            # 设置文本框属性
            text_frame.word_wrap = True
            text_frame.auto_size = MSO_AUTO_SIZE.SHAPE_TO_FIT_TEXT
            
            # 添加段落
            paragraphs = text_content.get('paragraphs', [])
            if paragraphs:
                for para_idx, para_data in enumerate(paragraphs):
                    if para_idx == 0:
                        p = text_frame.paragraphs[0]
                    else:
                        p = text_frame.add_paragraph()
                    
                    # 设置段落文本
                    para_text = para_data.get('text', '')
                    maotai_para_text = self.enhanced_replace_content(para_text)
                    
                    # 添加运行
                    runs = para_data.get('runs', [])
                    if runs:
                        for run_idx, run_data in enumerate(runs):
                            run_text = run_data.get('text', '')
                            maotai_run_text = self.enhanced_replace_content(run_text)
                            
                            if run_idx == 0:
                                p.text = maotai_run_text
                                if p.runs:
                                    run = p.runs[0]
                                    self._apply_font_formatting(run, run_data.get('font', {}))
                            else:
                                run = p.add_run()
                                run.text = maotai_run_text
                                self._apply_font_formatting(run, run_data.get('font', {}))
                    else:
                        p.text = maotai_para_text
            else:
                text_frame.text = maotai_text
            
            return content_replaced
            
        except Exception as e:
            return False
    
    def _apply_font_formatting(self, run, font_data: Dict[str, Any]):
        """应用字体格式"""
        try:
            font = run.font
            
            # 设置字体名称
            font_name = font_data.get('name')
            if font_name and font_name != 'None':
                if '+mj-ea' in font_name or '阿里巴巴普惠体' in font_name:
                    font.name = '微软雅黑'
                elif '思源黑体' in font_name:
                    font.name = '黑体'
                else:
                    font.name = '微软雅黑'  # 默认字体
            
            # 设置字体大小
            size_pt = font_data.get('size_pt')
            if size_pt and size_pt > 0:
                font.size = Pt(min(size_pt, 72))
            
            # 设置字体样式
            if font_data.get('bold'):
                font.bold = True
            if font_data.get('italic'):
                font.italic = True
            
            # 设置字体颜色（使用茅台红）
            font.color.rgb = RGBColor(200, 16, 46)
            
        except Exception:
            pass
    
    # 复用其他方法
    def _create_image_placeholder(self, slide, left: int, top: int, width: int, height: int):
        """创建图片占位符"""
        try:
            shape = slide.shapes.add_shape(1, left, top, width, height)
            fill = shape.fill
            fill.solid()
            fill.fore_color.rgb = RGBColor(200, 16, 46)
            if hasattr(shape, 'text_frame'):
                shape.text_frame.text = "茅台LOGO"
                shape.text_frame.paragraphs[0].font.color.rgb = RGBColor(255, 255, 255)
        except:
            pass
    
    def _create_table_shape(self, slide, shape_data: Dict[str, Any], left: int, top: int, width: int, height: int):
        """创建表格形状"""
        try:
            table_content = shape_data.get('table_content', {})
            rows = table_content.get('rows', 2)
            cols = table_content.get('columns', 2)
            
            table = slide.shapes.add_table(rows, cols, left, top, width, height).table
            
            cells_data = table_content.get('cells', [])
            for row_idx, row_data in enumerate(cells_data):
                if row_idx >= rows:
                    break
                for col_idx, cell_data in enumerate(row_data):
                    if col_idx >= cols:
                        break
                    
                    cell = table.cell(row_idx, col_idx)
                    cell_text = cell_data.get('text', '')
                    maotai_text = self.enhanced_replace_content(cell_text)
                    cell.text = maotai_text
        except:
            pass
    
    def _create_chart_placeholder(self, slide, left: int, top: int, width: int, height: int):
        """创建图表占位符"""
        try:
            shape = slide.shapes.add_shape(1, left, top, width, height)
            fill = shape.fill
            fill.solid()
            fill.fore_color.rgb = RGBColor(245, 245, 245)
            if hasattr(shape, 'text_frame'):
                shape.text_frame.text = "茅台业绩图表"
        except:
            pass
    
    def _create_group_shapes(self, slide, shape_data: Dict[str, Any]):
        """创建组合形状"""
        group_content = shape_data.get('group_content', {})
        grouped_shapes = group_content.get('grouped_shapes', [])
        for grouped_shape in grouped_shapes:
            try:
                self._recreate_shape_enhanced(slide, grouped_shape)
            except:
                continue
    
    def _create_shape_placeholder(self, slide, left: int, top: int, width: int, height: int):
        """创建通用形状占位符"""
        try:
            shape = slide.shapes.add_shape(1, left, top, width, height)
            fill = shape.fill
            fill.solid()
            fill.fore_color.rgb = RGBColor(255, 215, 0)
            fill.transparency = 0.8
        except:
            pass


def main():
    """主函数"""
    print("🍷 增强版茅台PPT复刻器")
    print("=" * 60)
    
    # 查找提取的JSON文件
    extracted_dir = Path("extracted_pptx_data")
    if not extracted_dir.exists():
        print("❌ 未找到 extracted_pptx_data 目录!")
        return
    
    json_files = list(extracted_dir.glob("*_extracted.json"))
    individual_files = [f for f in json_files if not f.name.startswith("all_")]
    
    if not individual_files:
        print("❌ 未找到提取的JSON文件!")
        return
    
    json_file = individual_files[0]
    print(f"📁 使用数据文件: {json_file.name}")
    
    # 创建增强版复刻器
    recreator = EnhancedMaotaiRecreator()
    
    # 开始复刻
    try:
        output_file = recreator.recreate_presentation_enhanced(str(json_file))
        
        print(f"\n🎉 增强版复刻完成!")
        print(f"📄 输出文件: {output_file}")
        print(f"📊 文件大小: {os.path.getsize(output_file) / 1024 / 1024:.2f} MB")
        
    except Exception as e:
        print(f"❌ 复刻过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
